fileFormatVersion: 2
guid: d564abf494f8a47898b93d76efc36367
importerOverride:
  nativeImporterType: 2089858483
  scriptedImporterType:
    serializedVersion: 2
    Hash: b10b55a1aa6a1aa32521bc0cab59632e
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: b2a9591990af98743ba3ff7cf1000886, type: 3}
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 1
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 1
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 512
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 0
    wrapV: 0
    wrapW: 0
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  spriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
    spritePosition: {x: 0, y: 0}
  mosaicSpriteImportData:
  - name: PoopHeap
    originalName: 
    pivot: {x: 0.5, y: 0.5}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 530
      y: 4
      width: 115
      height: 39
    spriteID: 90bbb183e5e9e41e0b929f45f11eb864
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: -446, y: -177}
    spritePosition: {x: 976, y: 191}
  - name: RiceHeap3
    originalName: 
    pivot: {x: 0.5, y: 0.5}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 4
      y: 102
      width: 518
      height: 38
    spriteID: f4a782650baaf498483e30300cc8259f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: -779, y: -77}
    spritePosition: {x: 783, y: 179}
  - name: RiceHeap2
    originalName: 
    pivot: {x: 0.5, y: 0.5}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 4
      y: 54
      width: 517
      height: 40
    spriteID: 940eb32bb46944c10806e25770109363
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: -779, y: -129}
    spritePosition: {x: 783, y: 183}
  - name: RiceHeap1
    originalName: 
    pivot: {x: 0.5, y: 0.5}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 518
      height: 42
    spriteID: 75f31b79c4bfd455db0d147d15e223e3
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: -779, y: -179}
    spritePosition: {x: 783, y: 183}
  rigSpriteImportData:
  - name: "\u56FE\u5C42 9"
    originalName: 
    pivot: {x: 0.5, y: 0.5}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 4
      y: 102
      width: 518
      height: 38
    spriteID: 239c44a2c09544613bd600f8c290d78a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: -779, y: -77}
    spritePosition: {x: 783, y: 179}
  - name: "\u56FE\u5C42 9"
    originalName: 
    pivot: {x: 0.5, y: 0.5}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 4
      y: 54
      width: 517
      height: 40
    spriteID: 6c01fe4546b1e4b6dad3564a22c4d224
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: -779, y: -129}
    spritePosition: {x: 783, y: 183}
  - name: "\u56FE\u5C42 9"
    originalName: 
    pivot: {x: 0.5, y: 0.5}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 518
      height: 42
    spriteID: 64fd9baecc7aa45e2886f64934bacf8e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: -779, y: -179}
    spritePosition: {x: 783, y: 183}
  characterData:
    bones: []
    parts: []
    dimension: {x: 0, y: 0}
    characterGroups: []
    pivot: {x: 0, y: 0}
    boneReadOnly: 0
  sharedRigSpriteImportData: []
  sharedRigCharacterData:
    bones: []
    parts: []
    dimension: {x: 0, y: 0}
    characterGroups: []
    pivot: {x: 0, y: 0}
    boneReadOnly: 0
  platformSettings:
  - name: DefaultTexturePlatform
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  - name: Standalone
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  - name: WebGL
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  mosaicLayers: 1
  characterMode: 0
  documentPivot: {x: 0, y: 0}
  documentAlignment: 7
  importHiddenLayers: 0
  layerMappingOption: 2
  generatePhysicsShape: 0
  paperDollMode: 0
  keepDupilcateSpriteName: 1
  skeletonAssetReferenceID: 
  pipeline: {instanceID: 0}
  pipelineVersion: 
  padding: 4
  spriteSizeExpand: 0
  spriteCategoryList:
    categories: []
  spritePackingTag: 
  resliceFromLayer: 0
  mosaicPSDLayers:
  - name: PoopHeap
    spriteName: PoopHeap
    isGroup: 0
    parentIndex: -1
    spriteID: 90bbb183e5e9e41e0b929f45f11eb864
    layerID: 7
    mosaicPosition: {x: 530, y: 4}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: RiceHeap3
    spriteName: RiceHeap3
    isGroup: 0
    parentIndex: -1
    spriteID: f4a782650baaf498483e30300cc8259f
    layerID: 5
    mosaicPosition: {x: 4, y: 102}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: RiceHeap2
    spriteName: RiceHeap2
    isGroup: 0
    parentIndex: -1
    spriteID: 940eb32bb46944c10806e25770109363
    layerID: 4
    mosaicPosition: {x: 4, y: 54}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: RiceHeap1
    spriteName: RiceHeap1
    isGroup: 0
    parentIndex: -1
    spriteID: 75f31b79c4bfd455db0d147d15e223e3
    layerID: 3
    mosaicPosition: {x: 4, y: 4}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: "\u56FE\u5C42 6"
    spriteName: 
    isGroup: 0
    parentIndex: -1
    spriteID: 733a7d8fd3bad49628c363f7c84993d6
    layerID: 2
    mosaicPosition: {x: 0, y: 0}
    flatten: 0
    isImported: 0
    isVisible: 0
  - name: "\u80CC\u666F"
    spriteName: 
    isGroup: 0
    parentIndex: -1
    spriteID: 49c0052a68c124c9e83cdeb91abd3dfe
    layerID: 1
    mosaicPosition: {x: 0, y: 0}
    flatten: 0
    isImported: 0
    isVisible: 0
  rigPSDLayers:
  - name: "\u56FE\u5C42 9"
    spriteName: "\u56FE\u5C42 9"
    isGroup: 0
    parentIndex: -1
    spriteID: 239c44a2c09544613bd600f8c290d78a
    layerID: 5
    mosaicPosition: {x: 4, y: 102}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: "\u56FE\u5C42 9"
    spriteName: "\u56FE\u5C42 9"
    isGroup: 0
    parentIndex: -1
    spriteID: 6c01fe4546b1e4b6dad3564a22c4d224
    layerID: 4
    mosaicPosition: {x: 4, y: 54}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: "\u56FE\u5C42 9"
    spriteName: "\u56FE\u5C42 9"
    isGroup: 0
    parentIndex: -1
    spriteID: 64fd9baecc7aa45e2886f64934bacf8e
    layerID: 3
    mosaicPosition: {x: 4, y: 4}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: "\u56FE\u5C42 6"
    spriteName: 
    isGroup: 0
    parentIndex: -1
    spriteID: 492a52fe96e2249b984e6953700968f8
    layerID: 2
    mosaicPosition: {x: 0, y: 0}
    flatten: 0
    isImported: 0
    isVisible: 0
  - name: "\u80CC\u666F"
    spriteName: 
    isGroup: 0
    parentIndex: -1
    spriteID: 6fa8fabbc8dd44802b5c8cb38919b9a6
    layerID: 1
    mosaicPosition: {x: 0, y: 0}
    flatten: 0
    isImported: 0
    isVisible: 0
  sharedRigPSDLayers: []
  pSDLayerImportSetting: []
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  spriteSizeExpandChanged: 0
  generateGOHierarchy: 0
  textureAssetName: 
  prefabAssetName: 
  spriteLibAssetName: 
  skeletonAssetName: 
  secondarySpriteTextures: []
