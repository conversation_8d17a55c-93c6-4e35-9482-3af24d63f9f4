fileFormatVersion: 2
guid: 4a3fce6e9d02241b1943ed48e2f13981
importerOverride:
  nativeImporterType: 2089858483
  scriptedImporterType:
    serializedVersion: 2
    Hash: b10b55a1aa6a1aa32521bc0cab59632e
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: b2a9591990af98743ba3ff7cf1000886, type: 3}
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 1
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 1
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 0
    wrapV: 0
    wrapW: 0
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  spriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
    spritePosition: {x: 0, y: 0}
  mosaicSpriteImportData: []
  rigSpriteImportData:
  - name: "\u56FE\u5C42 2"
    originalName: 
    pivot: {x: 0.5, y: 0.5}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 72
      height: 963
    spriteID: 32a20eead981640feb946bdd55d26c19
    spriteBone:
    - name: bone_3
      guid: eb4ac0df9be2f4946a53b018acf7498b
      position: {x: 403.40295, y: -0.0023287546, z: 0}
      rotation: {x: 0, y: 0, z: -0.0000029080802, w: 1}
      length: 316.15076
      parentId: 1
      color:
        serializedVersion: 2
        rgba: 4278255360
    - name: bone_2
      guid: 79ab79e8a958c457f845cce9c493c9e0
      position: {x: 186.06322, y: 0.000049324764, z: 0}
      rotation: {x: 0, y: 0, z: -0.0011451298, w: 0.99999934}
      length: 403.4029
      parentId: 2
      color:
        serializedVersion: 2
        rgba: 4278255615
    - name: bone_1
      guid: f99898105eaed47ba92044c81e6e8df3
      position: {x: 39.29315, y: 931.7498, z: 0}
      rotation: {x: 0, y: 0, z: -0.70710665, w: 0.707107}
      length: 186.06322
      parentId: -1
      color:
        serializedVersion: 2
        rgba: 4278190335
    spriteOutline: []
    vertices:
    - position: {x: 72, y: 40.94699}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 72, y: 81.89099}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 72, y: 122.836}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 72, y: 163.78201}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 72, y: 204.72699}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 72, y: 245.672}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 72, y: 286.61798}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 72, y: 327.562}
      boneWeight:
        weight0: 0.5796435
        weight1: 0.4203565
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 1
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 72, y: 368.508}
      boneWeight:
        weight0: 0.6491398
        weight1: 0.35086018
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 0
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 72, y: 409.453}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 72, y: 450.399}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 72, y: 491.342}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 72, y: 532.286}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 72, y: 573.232}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 72, y: 614.177}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 72, y: 655.121}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 72, y: 696.067}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 72, y: 737.013}
      boneWeight:
        weight0: 0.6559105
        weight1: 0.34408954
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 72, y: 777.958}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 72, y: 818.903}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 72, y: 859.847}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 72, y: 900.792}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 72, y: 941.739}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 72, y: 963}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 31.055, y: 963}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0, y: 963}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0, y: 922.053}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0, y: 881.109}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0, y: 840.164}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0, y: 799.218}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0, y: 758.273}
      boneWeight:
        weight0: 0.70979166
        weight1: 0.29020834
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 1
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0, y: 717.328}
      boneWeight:
        weight0: 0.62914366
        weight1: 0.37085634
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0, y: 676.382}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0, y: 635.438}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0, y: 594.492}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0, y: 553.547}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0, y: 512.601}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0, y: 471.658}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0, y: 430.714}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0, y: 389.768}
      boneWeight:
        weight0: 0.8229057
        weight1: 0.1770943
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 0
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0, y: 348.823}
      boneWeight:
        weight0: 0.5283323
        weight1: 0.4716677
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 0
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0, y: 307.879}
      boneWeight:
        weight0: 0.87601376
        weight1: 0.12398622
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 1
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0, y: 266.93298}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0, y: 225.987}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0, y: 185.04199}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0, y: 144.09698}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0, y: 103.153015}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0, y: 62.208008}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0, y: 21.260986}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0, y: 0}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 40.945, y: 0}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 72, y: 0}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    spritePhysicsOutline: []
    indices: 000000002f000000010000000c00000024000000230000000d000000220000000e0000000d00000023000000220000000e000000210000000f0000000e00000022000000210000000f00000020000000100000000f0000002100000020000000100000001f0000001100000010000000200000001f000000110000001e00000012000000110000001f0000001e000000120000001d00000013000000120000001e0000001d000000130000001c00000014000000130000001d0000001c000000140000001b00000015000000140000001c0000001b000000150000001a00000016000000150000001b0000001a000000160000001800000017000000160000001a000000180000000c000000230000000d0000000b00000025000000240000000b000000240000000c0000000a000000260000002500000000000000300000002f000000000000003200000030000000000000003300000032000000010000002e00000002000000010000002f0000002e000000020000002d00000003000000020000002e0000002d000000030000002c00000004000000030000002d0000002c000000040000002b00000005000000180000001a00000019000000040000002c0000002b000000050000002b0000002a000000060000002900000007000000060000002a0000002900000007000000280000000800000007000000290000002800000008000000270000000900000008000000280000002700000009000000260000000a0000000900000027000000260000000a000000250000000b000000050000002a00000006000000300000003200000031000000
    edges:
    - {x: 0, y: 1}
    - {x: 0, y: 51}
    - {x: 1, y: 2}
    - {x: 2, y: 3}
    - {x: 3, y: 4}
    - {x: 4, y: 5}
    - {x: 5, y: 6}
    - {x: 6, y: 7}
    - {x: 7, y: 8}
    - {x: 8, y: 9}
    - {x: 9, y: 10}
    - {x: 10, y: 11}
    - {x: 11, y: 12}
    - {x: 12, y: 13}
    - {x: 13, y: 14}
    - {x: 14, y: 15}
    - {x: 15, y: 16}
    - {x: 16, y: 17}
    - {x: 17, y: 18}
    - {x: 18, y: 19}
    - {x: 19, y: 20}
    - {x: 20, y: 21}
    - {x: 21, y: 22}
    - {x: 22, y: 23}
    - {x: 23, y: 24}
    - {x: 24, y: 25}
    - {x: 25, y: 26}
    - {x: 26, y: 27}
    - {x: 27, y: 28}
    - {x: 28, y: 29}
    - {x: 29, y: 30}
    - {x: 30, y: 31}
    - {x: 31, y: 32}
    - {x: 32, y: 33}
    - {x: 33, y: 34}
    - {x: 34, y: 35}
    - {x: 35, y: 36}
    - {x: 36, y: 37}
    - {x: 37, y: 38}
    - {x: 38, y: 39}
    - {x: 39, y: 40}
    - {x: 40, y: 41}
    - {x: 41, y: 42}
    - {x: 42, y: 43}
    - {x: 43, y: 44}
    - {x: 44, y: 45}
    - {x: 45, y: 46}
    - {x: 46, y: 47}
    - {x: 47, y: 48}
    - {x: 48, y: 49}
    - {x: 49, y: 50}
    - {x: 50, y: 51}
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
    spritePosition: {x: 476, y: 24}
  characterData:
    bones:
    - name: bone_1
      guid: f99898105eaed47ba92044c81e6e8df3
      position: {x: 515.29315, y: 955.7498, z: 0}
      rotation: {x: 0, y: 0, z: -0.70710665, w: 0.707107}
      length: 186.06322
      parentId: -1
      color:
        serializedVersion: 2
        rgba: 4278190335
    - name: bone_2
      guid: 79ab79e8a958c457f845cce9c493c9e0
      position: {x: 186.0632, y: 0.000030728115, z: 0}
      rotation: {x: 0, y: 0, z: -0.0011451211, w: 0.99999934}
      length: 403.4029
      parentId: 0
      color:
        serializedVersion: 2
        rgba: 4278255615
    - name: bone_3
      guid: eb4ac0df9be2f4946a53b018acf7498b
      position: {x: 403.4029, y: -0.0023297626, z: 0}
      rotation: {x: 0, y: 0, z: -0.0000029168114, w: 1}
      length: 316.15076
      parentId: 1
      color:
        serializedVersion: 2
        rgba: 4278255360
    parts:
    - spritePosition:
        x: 476
        y: 24
        width: 72
        height: 963
      spriteId: 32a20eead981640feb946bdd55d26c19
      bones: 020000000100000000000000
      parentGroup: 0
      order: 0
    dimension: {x: 0, y: 0}
    characterGroups: []
    pivot: {x: 0.5, y: 0}
    boneReadOnly: 0
  sharedRigSpriteImportData: []
  sharedRigCharacterData:
    bones: []
    parts: []
    dimension: {x: 0, y: 0}
    characterGroups: []
    pivot: {x: 0, y: 0}
    boneReadOnly: 0
  platformSettings: []
  mosaicLayers: 1
  characterMode: 1
  documentPivot: {x: 0.5, y: 0}
  documentAlignment: 7
  importHiddenLayers: 0
  layerMappingOption: 2
  generatePhysicsShape: 0
  paperDollMode: 0
  keepDupilcateSpriteName: 1
  skeletonAssetReferenceID: 
  pipeline: {instanceID: 0}
  pipelineVersion: 
  padding: 4
  spriteSizeExpand: 0
  spriteCategoryList:
    categories: []
  spritePackingTag: 
  resliceFromLayer: 0
  mosaicPSDLayers: []
  rigPSDLayers:
  - name: "\u56FE\u5C42 2"
    spriteName: "\u56FE\u5C42 2"
    isGroup: 0
    parentIndex: -1
    spriteID: 32a20eead981640feb946bdd55d26c19
    layerID: 8
    mosaicPosition: {x: 4, y: 4}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: "\u56FE\u5C42 1"
    spriteName: 
    isGroup: 0
    parentIndex: -1
    spriteID: 45ec58da49f6e42399a257c537a59574
    layerID: 7
    mosaicPosition: {x: 0, y: 0}
    flatten: 0
    isImported: 0
    isVisible: 0
  sharedRigPSDLayers: []
  pSDLayerImportSetting: []
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  spriteSizeExpandChanged: 0
  generateGOHierarchy: 0
  textureAssetName: 
  prefabAssetName: 
  spriteLibAssetName: 
  skeletonAssetName: 
  secondarySpriteTextures: []
