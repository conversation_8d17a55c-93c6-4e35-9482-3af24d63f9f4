fileFormatVersion: 2
guid: fa6b392d3d2ac4df2a104a2e656341d3
importerOverride:
  nativeImporterType: 2089858483
  scriptedImporterType:
    serializedVersion: 2
    Hash: b10b55a1aa6a1aa32521bc0cab59632e
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: b2a9591990af98743ba3ff7cf1000886, type: 3}
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 1
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 1
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 512
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  spriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
    spritePosition: {x: 0, y: 0}
  mosaicSpriteImportData:
  - name: Table
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 7
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 1154
      height: 571
    spriteID: 4454b85cd073347e49df9cd07c668f56
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
    spritePosition: {x: 0, y: 0}
  - name: Teapot
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 7
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 1167
      y: 4
      width: 322
      height: 198
    spriteID: 996c8fb3d103242339f08fbc82f2b53a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 1167, y: 4}
    spritePosition: {x: 0, y: 0}
  - name: Bowl
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 7
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 1498
      y: 4
      width: 194
      height: 98
    spriteID: a40f893d40fe8460eae29b5d7ae1ab1e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 1498, y: 4}
    spritePosition: {x: 0, y: 0}
  rigSpriteImportData:
  - name: Bowl
    originalName: 
    pivot: {x: 0.5, y: 0.5}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 1498
      y: 4
      width: 194
      height: 98
    spriteID: d570d83b31e624a1aad1ed5ab689b46d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 861, y: -819}
    spritePosition: {x: 637, y: 823}
  - name: Teapot
    originalName: 
    pivot: {x: 0.5, y: 0.5}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 1167
      y: 4
      width: 323
      height: 198
    spriteID: 79ef6598c9ef14dbf9a61d04be6b317c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: -40, y: -812}
    spritePosition: {x: 1207, y: 816}
  - name: Table
    originalName: 
    pivot: {x: 0.5, y: 0.5}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 1155
      height: 571
    spriteID: 93f29817641684b4388b55b437ccad39
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: -461, y: -256}
    spritePosition: {x: 465, y: 260}
  characterData:
    bones: []
    parts: []
    dimension: {x: 0, y: 0}
    characterGroups: []
    pivot: {x: 0, y: 0}
    boneReadOnly: 0
  sharedRigSpriteImportData: []
  sharedRigCharacterData:
    bones: []
    parts: []
    dimension: {x: 0, y: 0}
    characterGroups: []
    pivot: {x: 0, y: 0}
    boneReadOnly: 0
  platformSettings:
  - name: DefaultTexturePlatform
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  - name: Standalone
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  - name: WebGL
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  mosaicLayers: 1
  characterMode: 0
  documentPivot: {x: 0, y: 0}
  documentAlignment: 7
  importHiddenLayers: 0
  layerMappingOption: 1
  generatePhysicsShape: 0
  paperDollMode: 0
  keepDupilcateSpriteName: 1
  skeletonAssetReferenceID: 
  pipeline: {instanceID: 0}
  pipelineVersion: 
  padding: 4
  spriteSizeExpand: 0
  spriteCategoryList:
    categories: []
  spritePackingTag: 
  resliceFromLayer: 0
  mosaicPSDLayers:
  - name: Bowl
    spriteName: Bowl
    isGroup: 0
    parentIndex: -1
    spriteID: 49f4d1943d98d415aa62f3ac6e3f87b3
    layerID: 7
    mosaicPosition: {x: 1498, y: 4}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: Teapot
    spriteName: Teapot
    isGroup: 0
    parentIndex: -1
    spriteID: 963ade7a5c6744fcf988d8f480a631d5
    layerID: 6
    mosaicPosition: {x: 1167, y: 4}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: Table
    spriteName: Table
    isGroup: 0
    parentIndex: -1
    spriteID: d83c48c16b4cb4c8aae2ea6953e075d7
    layerID: 5
    mosaicPosition: {x: 4, y: 4}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: "\u56FE\u5C42 4"
    spriteName: 
    isGroup: 0
    parentIndex: -1
    spriteID: e36b6f053a16e490899061f06aabd795
    layerID: 4
    mosaicPosition: {x: 0, y: 0}
    flatten: 0
    isImported: 0
    isVisible: 0
  - name: "\u56FE\u5C42 13"
    spriteName: 
    isGroup: 0
    parentIndex: -1
    spriteID: 0b2be95f2055641c8bcadca0688ab64c
    layerID: 3
    mosaicPosition: {x: 0, y: 0}
    flatten: 0
    isImported: 0
    isVisible: 0
  - name: "\u56FE\u5C42 6"
    spriteName: 
    isGroup: 0
    parentIndex: -1
    spriteID: d8948206a12c74ce3abb49a7cd5aedc2
    layerID: 2
    mosaicPosition: {x: 0, y: 0}
    flatten: 0
    isImported: 0
    isVisible: 0
  - name: "\u80CC\u666F"
    spriteName: 
    isGroup: 0
    parentIndex: -1
    spriteID: 4b377c8d794b04d86bd80a7e0e2c1e5d
    layerID: 1
    mosaicPosition: {x: 0, y: 0}
    flatten: 0
    isImported: 0
    isVisible: 0
  rigPSDLayers:
  - name: Bowl
    spriteName: Bowl
    isGroup: 0
    parentIndex: -1
    spriteID: d570d83b31e624a1aad1ed5ab689b46d
    layerID: 7
    mosaicPosition: {x: 1498, y: 4}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: Teapot
    spriteName: Teapot
    isGroup: 0
    parentIndex: -1
    spriteID: 79ef6598c9ef14dbf9a61d04be6b317c
    layerID: 6
    mosaicPosition: {x: 1167, y: 4}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: Table
    spriteName: Table
    isGroup: 0
    parentIndex: -1
    spriteID: 93f29817641684b4388b55b437ccad39
    layerID: 5
    mosaicPosition: {x: 4, y: 4}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: "\u56FE\u5C42 4"
    spriteName: 
    isGroup: 0
    parentIndex: -1
    spriteID: 356df7e63ddcf46ebb5453650a527e2c
    layerID: 4
    mosaicPosition: {x: 0, y: 0}
    flatten: 0
    isImported: 0
    isVisible: 0
  - name: "\u56FE\u5C42 13"
    spriteName: 
    isGroup: 0
    parentIndex: -1
    spriteID: 70c8dff952dea49718c9f709fc3a83b3
    layerID: 3
    mosaicPosition: {x: 0, y: 0}
    flatten: 0
    isImported: 0
    isVisible: 0
  - name: "\u56FE\u5C42 6"
    spriteName: 
    isGroup: 0
    parentIndex: -1
    spriteID: 3048d213264504699a290afa5cdccf19
    layerID: 2
    mosaicPosition: {x: 0, y: 0}
    flatten: 0
    isImported: 0
    isVisible: 0
  - name: "\u80CC\u666F"
    spriteName: 
    isGroup: 0
    parentIndex: -1
    spriteID: d4e4078cb3f454e2187de987b799c01e
    layerID: 1
    mosaicPosition: {x: 0, y: 0}
    flatten: 0
    isImported: 0
    isVisible: 0
  sharedRigPSDLayers: []
  pSDLayerImportSetting: []
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  spriteSizeExpandChanged: 0
  generateGOHierarchy: 0
  textureAssetName: 
  prefabAssetName: 
  spriteLibAssetName: 
  skeletonAssetName: 
  secondarySpriteTextures: []
