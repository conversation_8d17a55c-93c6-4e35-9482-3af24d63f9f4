fileFormatVersion: 2
guid: c5e0d0cae36d24490be99acfe70dc63b
importerOverride:
  nativeImporterType: 2089858483
  scriptedImporterType:
    serializedVersion: 2
    Hash: b10b55a1aa6a1aa32521bc0cab59632e
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: b2a9591990af98743ba3ff7cf1000886, type: 3}
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 1
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 1
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 512
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  spriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
    spritePosition: {x: 0, y: 0}
  mosaicSpriteImportData:
  - name: Chain_Segment_1
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 7
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 164
      y: 4
      width: 28
      height: 123
    spriteID: 2f6773276f10d4af3b4efd08fe70a93c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 164, y: 4}
    spritePosition: {x: 22, y: 0}
  - name: Chain_Segment_2
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 7
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 84
      y: 4
      width: 72
      height: 126
    spriteID: 42e472670722d4f34a3e1ae2c3914efb
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 84, y: 4}
    spritePosition: {x: 0, y: 71}
  - name: Chain
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 7
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 72
      height: 197
    spriteID: abd96358ea41241f2bfd45c2b49f528b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
    spritePosition: {x: 0, y: 0}
  rigSpriteImportData:
  - name: Chain_Segment_1
    originalName: 
    pivot: {x: 0.5, y: 0.5}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 164
      y: 4
      width: 28
      height: 123
    spriteID: 18a780dd8c4e84fee942b6bcaee2b500
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 164, y: 4}
    spritePosition: {x: 22, y: 0}
  - name: Chain_Segment_2
    originalName: 
    pivot: {x: 0.5, y: 0.5}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 84
      y: 4
      width: 72
      height: 126
    spriteID: 947591dc17f66420ab25deb3b6df220e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 84, y: 4}
    spritePosition: {x: 0, y: 71}
  - name: Chain
    originalName: 
    pivot: {x: 0.5, y: 0.5}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 72
      height: 197
    spriteID: 68e9238f968ed440bb17e400ca534d96
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
    spritePosition: {x: 0, y: 0}
  characterData:
    bones: []
    parts: []
    dimension: {x: 0, y: 0}
    characterGroups: []
    pivot: {x: 0, y: 0}
    boneReadOnly: 0
  sharedRigSpriteImportData: []
  sharedRigCharacterData:
    bones: []
    parts: []
    dimension: {x: 0, y: 0}
    characterGroups: []
    pivot: {x: 0, y: 0}
    boneReadOnly: 0
  platformSettings:
  - name: DefaultTexturePlatform
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  - name: Standalone
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  - name: WebGL
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  mosaicLayers: 1
  characterMode: 0
  documentPivot: {x: 0, y: 0}
  documentAlignment: 7
  importHiddenLayers: 0
  layerMappingOption: 2
  generatePhysicsShape: 0
  paperDollMode: 0
  keepDupilcateSpriteName: 1
  skeletonAssetReferenceID: 
  pipeline: {instanceID: 0}
  pipelineVersion: 
  padding: 4
  spriteSizeExpand: 0
  spriteCategoryList:
    categories: []
  spritePackingTag: 
  resliceFromLayer: 0
  mosaicPSDLayers:
  - name: Chain_Segment_1
    spriteName: Chain_Segment_1
    isGroup: 0
    parentIndex: -1
    spriteID: 2f6773276f10d4af3b4efd08fe70a93c
    layerID: 11
    mosaicPosition: {x: 164, y: 4}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: Chain_Segment_2
    spriteName: Chain_Segment_2
    isGroup: 0
    parentIndex: -1
    spriteID: 42e472670722d4f34a3e1ae2c3914efb
    layerID: 10
    mosaicPosition: {x: 84, y: 4}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: Chain
    spriteName: Chain
    isGroup: 0
    parentIndex: -1
    spriteID: abd96358ea41241f2bfd45c2b49f528b
    layerID: 8
    mosaicPosition: {x: 4, y: 4}
    flatten: 0
    isImported: 1
    isVisible: 1
  rigPSDLayers:
  - name: Chain_Segment_1
    spriteName: Chain_Segment_1
    isGroup: 0
    parentIndex: -1
    spriteID: 18a780dd8c4e84fee942b6bcaee2b500
    layerID: 11
    mosaicPosition: {x: 164, y: 4}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: Chain_Segment_2
    spriteName: Chain_Segment_2
    isGroup: 0
    parentIndex: -1
    spriteID: 947591dc17f66420ab25deb3b6df220e
    layerID: 10
    mosaicPosition: {x: 84, y: 4}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: Chain
    spriteName: Chain
    isGroup: 0
    parentIndex: -1
    spriteID: 68e9238f968ed440bb17e400ca534d96
    layerID: 8
    mosaicPosition: {x: 4, y: 4}
    flatten: 0
    isImported: 1
    isVisible: 1
  sharedRigPSDLayers: []
  pSDLayerImportSetting: []
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  spriteSizeExpandChanged: 0
  generateGOHierarchy: 0
  textureAssetName: 
  prefabAssetName: 
  spriteLibAssetName: 
  skeletonAssetName: 
  secondarySpriteTextures: []
