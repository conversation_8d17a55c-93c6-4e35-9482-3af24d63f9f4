
using System;
using UnityEngine;

public class LerpTest : MonoBehaviour
{
  struct TestFeature
  {

  }

  struct Feature
  {
    public Vector3? position;
    public float x;
  }

  [SerializeField]
  private Transform from;
  [SerializeField]
  private Transform to;
  [SerializeField]
  private Transform third;

  private AnimationClip clip;

  private TweenAnim<Feature> _tween;

  void Start()
  {
    _tween = gameObject.TweenAnim<Feature>();
    WithCrossFrom();
    Invoke("WithCrossTo", 1);
  }

  void TestBase()
  {
    _tween
      .SetOnChange(f => {
        transform.position = f.position ?? transform.position;
      })
      .BeginBatchChange(new() { duration = 2f, speed = 4f, loopCount = -1, pingpong = true })
      // .SetTimeTrack(t => t.position, Tween.CreateTimeTrackChange(new Vector3[] { from.position, to.position, third.position }))
      .EndBatchChange()
      .Start();
  }
  void WithDelay()
  {
    _tween
      .SetOnChange(f => {
        Debug.Log(f.x);
        transform.position = f.position ?? transform.position;
      })
      .BeginBatchChange(new() { duration = 2f, speed = 2f, loopCount = -1, pingpong = true })
      .SetTimeTrack(t => t.position, Tween.CreateTimeTrackClip(new Vector3?[] { from.position, to.position }, delay: 2f))
      .SetTimeTrack(t => t.x, Tween.CreateTimeTrackClip(new [] { 0f, 1f }))
      .EndBatchChange()
      .Start();
  }
  void WithCrossFrom()
  {
    _tween
      .SetOnChange(f => {
        // Debug.Log(f.x);
        transform.position = f.position ?? transform.position;
      })
      .BeginBatchChange(new() { 
        duration = 2f, 
        speed = 1f,
        crossDuration = 0.5f,
      })
      .SetTimeTrack(t => t.position, Tween.CreateTimeTrackClip(new Vector3?[] { from.position, to.position }))
      .EndBatchChange()
      .Start();
  }
  void WithCrossTo()
  {
    _tween
      .SetOnChange(f => {
        // Debug.Log(f.x);
        transform.position = f.position ?? transform.position;
      })
      .BeginBatchChange(new() { 
        duration = 2f, 
        speed = 1f,
        crossDuration = 0.5f,
        crossEaseType = EaseType.EaseInOutSine,
      })
      .SetTimeTrack(t => t.position, Tween.CreateTimeTrackClip(new Vector3?[] { third.position }))
      .EndBatchChange()
      .Start();
  }



}