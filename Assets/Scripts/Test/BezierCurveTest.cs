
using System;
using UnityEditor;
using UnityEngine;
using UnityEngine.Splines;

public class BezierCurveTest : MonoBehaviour
{
  public Vector3 startPoint;
  public Vector3 startTangent;
  public Vector3 endTangent;
  public Vector3 endPoint;
  [SerializeField] int precision = 50;

  private SplineContainer splineContainer;

  void Start()
  {
    splineContainer = GetComponent<SplineContainer>();
    var dir1 = startTangent - startPoint;
    var dir2 = endPoint - endTangent;
    var knot1 = new BezierKnot(startPoint, new Vector3(0, 0, 0), new Vector3(0, 0, dir1.magnitude), Quaternion.LookRotation(dir1, -Vector3.forward));
    var knot2 = new BezierKnot(endPoint, new Vector3(0, 0, -dir2.magnitude), new Vector3(0, 0, 0), Quaternion.LookRotation(dir2, -Vector3.forward));
    var spline = new Spline(new [] { knot1, knot2 });
    splineContainer.AddSpline(spline);
  }

  public Vector3 Evaluate(float t)
  {
    return GetValue(startPoint, startTangent, endTangent, endPoint, t);
  }

  public Vector3 EvaluateTangent(float t)
  {
    return GetTangent(startPoint, startTangent, endTangent, endPoint, t);
  }

  private Vector3 GetTangent(Vector3 a, Vector3 b, Vector3 c, Vector3 d, float t)
  {
    // dP(t) / dt =  -3(1-t)^2 * P0 + 3(1-t)^2 * P1 - 6t(1-t) * P1 - 3t^2 * P2 + 6t(1-t) * P2 + 3t^2 * P3 
    var mt = 1 - t;
    var t2 = t * t;
    var mt2 = mt * mt;

    return -3 * mt2 * a + 3 * mt2 * b - 6 * t * mt * b - 3 * t2 * c + 6 * t * mt * c + 3 * t2 * d;
  }

  private Vector3 GetValue(Vector3 a, Vector3 b, Vector3 c, Vector3 d, float t)
  {
    // var m = Vector3.Lerp(a, b, t);
    // var n = Vector3.Lerp(b, c, t);
    // var o = Vector3.Lerp(c, d, t);
    // var p = Vector3.Lerp(m, n, t);
    // var q = Vector3.Lerp(n, o, t);
    // return Vector3.Lerp(p, q, t);

    // https://pomax.github.io/bezierinfo/#abc
    // P(t) = (1 - t)^3 * P0 + 3t(1-t)^2 * P1 + 3t^2 (1-t) * P2 + t^3 * P3
    var mt = 1 - t;

    var t2 = t * t;
    var t3 = t2 * t;
    var mt2 = mt * mt;
    var mt3 = mt2 * mt;

    return mt3 * a + 3 * t * mt2 * b + 3 * t2 * mt * c + t3 * d;
  }

  void OnDrawGizmos()
  {
    Handles.color = Color.red;
    Handles.DrawBezier(startPoint, endPoint, startTangent, endTangent, Color.red, null, 5);
  }

  // void Draw()
  // {
  //   var points = new Vector3[precision];
  //   for (int i = 0; i < precision; ++i)
  //   {
  //     var percent = (float)i / (precision - 1);
  //     points[i] = GetValue(startPoint, startTangent, endTangent, endPoint, percent);
  //   }
  //   lineRenderer.positionCount = points.Length;
  //   lineRenderer.SetPositions(points);
  // }

}