
using System;
using UnityEngine;

public class TweenArrayTest : MonoBehaviour
{
  class Feature
  {
    public float[] positions;
  }

  LineRenderer lineRenderer;
  TweenAnim<Feature> _tween;

  void Start()
  {
    lineRenderer = GetComponent<LineRenderer>();

    _tween = gameObject.TweenAnim<Feature>();

    var initialPositions = new float[] { 0f, 1f };
    var targetPositions = new float[] { 1f, 0f };

    _tween
      .SetOnChange(f => {
        Debug.Log(string.Join(", ", f.positions));
      })
      .BeginBatchChange(new () { duration = 1f})
      .SetTimeTrack(f => f.positions, Tween.CreateTimeTrackClip(new [] { initialPositions, targetPositions }))
      .EndBatchChange()
      .Start();
  }

}