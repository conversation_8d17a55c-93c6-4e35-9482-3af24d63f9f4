using UnityEditor;
using UnityEngine;

[CustomEditor(typeof(BezierCurveTest))]
public class BezierCurveTestEditor : Editor
{
    private BezierCurveTest be;
    void OnEnable()
    {
      be = (BezierCurveTest)target;
    }

    void OnSceneGUI()
    {
        be.startPoint = Handles.PositionHandle(be.startPoint, Quaternion.identity);
        be.endPoint = Handles.PositionHandle(be.endPoint, Quaternion.identity);
        be.startTangent = Handles.PositionHandle(be.startTangent, Quaternion.identity);
        be.endTangent = Handles.PositionHandle(be.endTangent, Quaternion.identity);

        // Visualize the tangent lines
        Handles.DrawDottedLine(be.startPoint, be.startTangent, 5);
        Handles.DrawDottedLine(be.endPoint, be.endTangent, 5);

        Handles.DrawBezier(be.startPoint, be.endPoint, be.startTangent, be.endTangent, Color.red, null, 5f);

    }

    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();
    }
    void ChangePosition(ref Vector3 position)
    {
        // 使用Handles创建可拖拽的位置控制点
        EditorGUI.BeginChangeCheck();
        // Vector3 newPosition = Handles.PositionHandle(gizmo.transform.position, Quaternion.identity);

        Vector3 newPosition = Handles.FreeMoveHandle(
            position,
            1f, Vector3.zero, Handles.SphereHandleCap
        );

        if (EditorGUI.EndChangeCheck())
        {
            Undo.RecordObject(be, "Move Target Object");

            // 更新目标对象位置
            position = newPosition;
        }


    }
}