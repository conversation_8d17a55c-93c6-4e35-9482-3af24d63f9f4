// using System;
// using System.Collections.Generic;
// using System.Linq;
// using Unity.VisualScripting;
// using UnityEngine;

// public class MovingLegInfo
// {
//   public LegInfo leg;
//   public LegMoveState moveState;
//   public Vector2 startPosition;
//   public Vector2 moveVector;
//   public float t;
//   public float stepTime;
// }

// public class TwoLegGroupsWalkController : MonoBehaviour
// {
//   [SerializeField] LegInfo[] legInfos;
//   [SerializeField] float speed = 1f;
//   [SerializeField] float stepSpacing = 1f;
//   [SerializeField] float stepHeight = .2f;
//   [SerializeField] float stepTime = .8f;
//   [SerializeField] AnimationCurve stepCurve;
//   private Vector2 FacingDirection = Vector2.right;
//   private List<MovingLegInfo> movingLegInfos = new();

//   private bool isMoving = false;

//   void Start()
//   {
//     // movingLegInfos = legInfos.Select(l => {
//     //   var hit = Physics2D.Raycast((Vector2)l.root.position + 0.5f * stepSpacing * FacingDirection, Vector2.down, 10f, GameConfig.Instance.groundMask);
//     //   var moveVector = hit.point - (Vector2)l.target.position;

//     //   if (hit.collider == null)
//     //   {
//     //     // TODO...
//     //   }

//     //   return new MovingLegInfo{
//     //     leg = l,
//     //     moveState = LegMoveState.Fixed,
//     //     t = 0f,
//     //     startPosition = l.target.position,

//     //     stepTime = stepTime * Mathf.Clamp01(moveVector.magnitude / stepSpacing),
//     //     moveVector = moveVector        
//     //   };
//     // }).ToList();
//   }

//   void Update()
//   {
//     if (Input.GetKeyDown(KeyCode.N) && !isMoving)
//     {
//       isMoving = true;
//       StartMove();
//     }

//     Move();
//   }

//   void StartMove()
//   {
//     // find the leg which has max back bias 
//     var currentMoveLeg = legInfos.Aggregate((acc, curr) =>
//     {
//       var accOffset = acc.target.position.x - acc.root.position.x;
//       var currOffset = curr.target.position.x - curr.root.position.x;
//       if (FacingDirection.x > 0)
//       {
//         return accOffset < currOffset ? acc : curr;
//       }
//       return accOffset > currOffset ? acc : curr;
//     });

//     movingLegInfos = legInfos.Select(l => {
//       LegMoveState legMoveState = LegMoveState.Fixed;

//       if (l == currentMoveLeg)
//       {
//         legMoveState = LegMoveState.Moving;
//       }
//       else if (l.legType == currentMoveLeg.legType)
//       {
//         legMoveState = LegMoveState.Ready;
//       }

//       RaycastHit2D hit;
//       if (legMoveState == LegMoveState.Moving)
//       {
//         // move forward
//         hit = Physics2D.Raycast((Vector2)l.root.position + 0.5f * stepSpacing * FacingDirection, Vector2.down, 10f, GameConfig.Instance.groundMask);
//       }
//       else
//       {
//         // move backward
//         hit = Physics2D.Raycast((Vector2)l.root.position - 0.5f * stepSpacing * FacingDirection, Vector2.down, 10f, GameConfig.Instance.groundMask);
//       }

//       // TODO should I fix the hit collider is null ??

//       var moveVector = hit.point - (Vector2)l.target.position;

//       return new MovingLegInfo{
//         leg = l,
//         t = 0,
//         moveState = legMoveState,
//         startPosition = l.target.position,
//         stepTime = stepTime * Mathf.Clamp01(moveVector.magnitude / stepSpacing),
//         moveVector = moveVector,
//       };
//     }).ToList();
//   }

//   void Move()
//   {
//     // the moving leg need move forward in local space
//     foreach (var movingLegInfo in movingLegInfos)
//     {
//       MoveLeg(movingLegInfo);
//     }
//   }

//   void MoveLeg(MovingLegInfo movingLegInfo)
//   {
//     movingLegInfo.t += Time.deltaTime;
//     var t = Mathf.Clamp01(movingLegInfo.t / movingLegInfo.stepTime);
//     movingLegInfo.leg.target.localPosition = movingLegInfo.startPosition + t * movingLegInfo.moveVector;

//     if (movingLegInfo.t >= stepTime)
//     {
//       // remove from movingLegInfos
//       movingLegInfos.Remove(movingLegInfo);
//     }
//   }

// }
