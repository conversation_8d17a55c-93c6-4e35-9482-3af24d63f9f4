// using UnityEditor;
// using UnityEngine;

// public class TwoLegMove : MonoBehaviour
// {
//     [SerializeField] AnimationCurve horizontalCurve;
//     [SerializeField] AnimationCurve verticalCurve;
//     [SerializeField] Transform legLTarget;
//     [SerializeField] Transform legRTarget;
//     [SerializeField] Transform hip;

//     [SerializeField] Transform hipCenter;
//     [SerializeField] float footSpacing;
//     [SerializeField] float stepHeight;
//     [SerializeField] float speed = 1f;


//     private Transform forwardTarget;
//     private Transform backwardTarget;
//     private Vector2 forwardStartPoint;
//     private Vector2 backwardStartPoint;
//     private float forwardStartSpeed;
//     private float backwardStartSpeed;
//     private float moveSpeed = 0;

//     private bool isMoving = false;

//     void Start()
//     {
        
//     }

//     void StartMove()
//     {
//       // which leg should step first?
//       forwardTarget = legRTarget;
//       if (legLTarget.position.x < legRTarget.position.x)
//       {
//         forwardTarget = legLTarget;
//       }

//       backwardTarget = legLTarget == forwardTarget ? legRTarget : legLTarget;

//       forwardStartPoint = forwardTarget.position;
//       backwardStartPoint = backwardTarget.position;

//       forwardStartSpeed = 0f;
//       backwardStartSpeed = 0f;

//       moveSpeed = 0f;

//       isMoving = true;
//     }

//     // Update is called once per frame
//     void Update()
//     {
//       if (Input.GetKeyDown(KeyCode.N))
//       {
//         if (isMoving)
//         {
//           isMoving = false;
//         }
//         else
//         {
//           EditorApplication.isPaused = true;
//           StartMove();
//         }
//       }

//       if (isMoving)
//       {
//         Move();
//       }
//     }

//     void Move()
//     {
//       moveSpeed += Time.deltaTime * speed;

//       var maxForward = new Vector2(hipCenter.position.x + footSpacing * 0.5f, forwardStartPoint.y);
//       var maxBackward = new Vector2(hipCenter.position.x - footSpacing * 0.5f, forwardStartPoint.y);

//       forwardTarget.position = Vector2.Lerp(forwardStartPoint, maxForward, moveSpeed);

//       // var oldPosition = backwardTarget.position;
//       backwardTarget.position = Vector2.Lerp(backwardStartPoint, maxBackward, moveSpeed);
//       // var moveDistance = oldPosition.x - backwardTarget.position.x;
//       // transform.position += new Vector3(moveDistance, 0, 0);
//     }

//     // private void OnDrawGizmos()
//     // {
//     //     // 设置Gizmo颜色
//     //     Gizmos.color = Color.cyan;
        
//     //     // 绘制一个立方体作为Gizmo标志
//     //     Gizmos.DrawCube(transform.position, Vector3.one * 0.5f);
        
//     //     // 如果不是在编辑模式下，直接返回
//     //     if (!Application.isEditor || Application.isPlaying)
//     //         return;
            
//     //     // 获取当前事件
//     //     Event currentEvent = Event.current;
//     //     int controlID = GUIUtility.GetControlID(FocusType.Passive);
        
//     //     // 处理鼠标事件
//     //     switch (currentEvent.type)
//     //     {
//     //         case EventType.MouseDown:
//     //             // 检查是否点击了Gizmo
//     //             if (HandleUtility.nearestControl == controlID && currentEvent.button == 0)
//     //             {
//     //                 GUIUtility.hotControl = controlID;
//     //                 currentEvent.Use();
//     //             }
//     //             break;
                
//     //         case EventType.MouseDrag:
//     //             // 如果正在拖动Gizmo
//     //             if (GUIUtility.hotControl == controlID)
//     //             {
//     //                 // 获取鼠标在世界空间中的位置
//     //                 Ray ray = HandleUtility.GUIPointToWorldRay(currentEvent.mousePosition);
//     //                 Plane plane = new Plane(Vector3.up, transform.position);
                    
//     //                 if (plane.Raycast(ray, out float distance))
//     //                 {
//     //                     Vector3 newPosition = ray.GetPoint(distance);
                        
//     //                     // 更新Gizmo位置
//     //                     transform.position = newPosition;
                        
//     //                     // 更新目标Transform的位置
//     //                     if (targetTransform != null)
//     //                     {
//     //                         targetTransform.position = newPosition;
//     //                         EditorUtility.SetDirty(targetTransform); // 标记为已修改
//     //                     }
                        
//     //                     // 强制场景重绘
//     //                     SceneView.RepaintAll();
//     //                 }
                    
//     //                 currentEvent.Use();
//     //             }
//     //             break;
                
//     //         case EventType.MouseUp:
//     //             // 释放控制
//     //             if (GUIUtility.hotControl == controlID && currentEvent.button == 0)
//     //             {
//     //                 GUIUtility.hotControl = 0;
//     //                 currentEvent.Use();
//     //             }
//     //             break;
                
//     //         case EventType.Repaint:
//     //             // 设置控制ID用于点击检测
//     //             HandleUtility.AddControl(controlID, HandleUtility.DistanceToCircle(transform.position, 0.5f));
//     //             break;
//     //     }
//     // }

//     void OnDrawGizmos()
//     {
//       Gizmos.color = Color.cyan;
//       if (hipCenter != null)
//       {
//         var leftPosition = (Vector2)hipCenter.position + 0.5f * footSpacing * Vector2.left;
//         var rightPosition = (Vector2)hipCenter.position + 0.5f * footSpacing * Vector2.right;

//         GizmosExt.DrawLine(leftPosition, rightPosition, Color.yellow);

//         if (legLTarget != null)
//         {
//           var height = Mathf.Abs(hipCenter.position.y - legLTarget.position.y);
//           GizmosExt.DrawLine(leftPosition, leftPosition + Vector2.down * height, Color.yellow);
//         }

//         if (legRTarget != null)
//         {
//           var height = Mathf.Abs(hipCenter.position.y - legRTarget.position.y);
//           GizmosExt.DrawLine(rightPosition, rightPosition + Vector2.down * height, Color.yellow);
//         }


//       }
//       // if (legStepCenter != null)
//       // {
//       //   GizmosExt.DrawLine(legStepCenter.position, (Vector2)legStepCenter.position + Vector2.up * 2, Color.yellow);
//       // }
//     }
// }
