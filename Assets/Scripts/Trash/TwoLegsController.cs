// using System;
// using System.Collections.Generic;
// using System.Linq;
// using Unity.VisualScripting;
// using UnityEditor;
// using UnityEngine;

// public enum LegType
// {
//   Left,
//   Right
// }

// public enum LegMoveState
// {
//   Ready,
//   Moving,
//   Fixed,
// }

// [System.Serializable]
// public class LegInfo
// {
//   public Transform root;
//   public Transform target;
//   public LegType legType;
// }

// public class MoveLegInfo
// {
//   public LegInfo leg;
//   public Vector2 startPosition;
//   public Vector2 endPosition;
//   public float lerp;
//   public LegMoveState moveState;
// }

// public class TwoLegsController : MonoBehaviour
// {
//   [SerializeField] LegInfo[] legs;
//   [SerializeField] float speed = 1f;
//   [SerializeField] float footSpacing = 1f;
//   [SerializeField] float stepHeight = .2f;
//   private Queue<MoveLegInfo> readyMoveLegs = new();

//   void Start()
//   {

//   }

//   // Update is called once per frame
//   void Update()
//   {
//     for (var i = 0; i < legs.Length; ++i)
//     {
//       var leg = legs[i];
//       var hit = Physics2D.Raycast((Vector2)leg.root.position, Vector2.down, 10f, GameConfig.Instance.groundMask);
//       if (hit.collider != null)
//       {
//         // > half pace, = ready to move
//         if (Vector2.Distance(leg.target.position, hit.point) > footSpacing * 0.6f && hit.point.x > leg.target.position.x)
//         {
//           if (!readyMoveLegs.Any(movingLeg => ReferenceEquals(movingLeg.leg, leg)))
//           {

//             var targetHit = Physics2D.Raycast((Vector2)leg.root.position + Vector2.right * (2.0f - 0.6f), Vector2.down, 10f, GameConfig.Instance.groundMask);

//             if (targetHit.collider != null)
//             {
//               readyMoveLegs.Enqueue(new MoveLegInfo
//               {
//                 leg = leg,
//                 startPosition = leg.target.position,
//                 endPosition = targetHit.point,
//                 lerp = 0f,
//                 moveState = LegMoveState.Ready
//               });
//             }
//           }
//         }
//       }
//     }

//     //
//     if (readyMoveLegs.Count > 0)
//     {
//       var moveLeg = readyMoveLegs.First();
//       if (moveLeg.lerp >= 1f)
//       {
//         readyMoveLegs.Dequeue();
//       }


//       var newPos = Vector2.Lerp(moveLeg.startPosition, moveLeg.endPosition, moveLeg.lerp);
//       var offset = Mathf.Sin(Mathf.Clamp01(moveLeg.lerp) * Mathf.PI) * stepHeight;
//       newPos.y += offset;
//       moveLeg.leg.target.position = newPos;
//       moveLeg.lerp += speed * Time.deltaTime;

//     }

//   }

//   void OnDrawGizmos()
//   {
//     // Gizmos.color = Color.cyan;
//     // if (legLCaster != null)
//     // {
//     //   GizmosExt.DrawLine(legLCaster.position, legLCaster.position + Vector3.down * 10f, Color.yellow);
//     // }
//     // if (legRCaster != null)
//     // {
//     //   GizmosExt.DrawLine(legRCaster.position, legRCaster.position + Vector3.down * 10f, Color.yellow);
//     // }
//   }
// }
