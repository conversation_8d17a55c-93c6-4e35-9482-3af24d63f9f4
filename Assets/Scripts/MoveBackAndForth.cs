
using UnityEngine;

public class MoveBackAndForth : MonoBehaviour
{
  [SerializeField] float stepSpacing;
  [SerializeField] float duration;
  [SerializeField] float forwardPercent = 0.35f;

  [SerializeField] Vector2 center;
  [SerializeField] float offset;

  private AnimationCurve curve = new();

  private float time = 0;

  void Awake()
  {
    transform.localPosition = center;



    Keyframe key1 = new(0f, -1f)
    {
      inTangent = 0f,
      outTangent = 0f
    };

    Keyframe key2 = new(forwardPercent, 1f)
    {
      inTangent = 0f,
      outTangent = (-1f - 1f) / (1f - forwardPercent)
    };


    Keyframe key3 = new(1f, -1f)
    {
      inTangent = key2.outTangent,
      outTangent = 0f,
    };

    curve.AddKey(key1);
    curve.AddKey(key2);
    curve.AddKey(key3);

    curve.preWrapMode = WrapMode.Loop;
    curve.postWrapMode = WrapMode.Loop;
  }

  void Update()
  {
    time += Time.deltaTime;
    var p = Mathf.Clamp01(time / duration);

    var offset = GetCurveOffset(p);

    var position = new Vector2(offset + center.x, center.y);
    transform.localPosition = position;

    if (time >= duration)
    {
      time = 0;
    }
  }

  float GetSinOffset(float p)
  {
    return Mathf.Sin(p * 2 * Mathf.PI) * stepSpacing * 0.5f;
  }

  float GetCurveOffset(float p)
  {
    return curve.Evaluate(p + offset) * stepSpacing * 0.5f;
  }

  void OnDrawGizmos()
  {
    Vector2 globalCenter = (Vector2)transform.parent.position + center;
    GizmosExt.DrawLine(globalCenter - 0.5f * stepSpacing * Vector2.right, globalCenter + 0.5f * stepSpacing * Vector2.right, Color.yellow);
  }
}