
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Half;
using Unity.VisualScripting;
using UnityEditor.Rendering;
using UnityEngine;

// [System.Serializable]
// public class WalkingLeg {
//   public Transform root;
//   public Transform effector;
//   public Transform target;
//   public float offset;
// }

public class BaseLegWalkController : MonoBehaviour
{
  public delegate Vector2 MoveAction(float t);
  public delegate void DoAction(float t);

  [Header("Leg")]
  [SerializeField] WalkingLeg[] walkingLegs;
  [SerializeField] float stepSpacing;
  [SerializeField] float stepDuration;
  // 0.35 for four, 0.5 for two
  [SerializeField] float forwardPercent = 0.5f;
  [SerializeField] float stepHeight;
  [Header("Hip")]
  [SerializeField] Transform hip;
  [SerializeField] float hipSpacing;
  [SerializeField] float hipOffsetX;
  [SerializeField] float baseHipOffsetY;

  [Header("Other")]
  [SerializeField] LayerMask groundLayerMask;


  Vector2 FacingDirection = Vector2.right;

  AnimationCurve stepCurve = new();
  AnimationCurve verticalMovingCurve = new();
  [SerializeField] AnimationCurve hipCurve = new();

  private float time = 0;

  void Awake()
  {
    CalculateStepCurve();
    CalculateVerticalCurve();
    CalculateHipCurve();
  }

  // void OnDrawGizmos()
  // {
  //   // leg move range
  //   Vector2? legCenter = null;
  //   foreach (var leg in walkingLegs)
  //   {
  //     var hit = Physics2D.Raycast(transform.TransformPoint(new Vector2(leg.centerLine, 100f)), Vector2.down, 10000f, groundLayerMask);
  //     Gizmos.color = Color.cyan;
  //     Gizmos.DrawSphere(hit.point, 0.1f);
  //     var tangent = Vector2.Perpendicular(hit.normal);

  //     // GizmosExt.DrawLine(hit.point, hit.point + 0.5f * stepSpacing * tangent, Color.yellow);
  //     GizmosExt.DrawLine(hit.point - 0.5f * stepSpacing * tangent, hit.point + 0.5f * stepSpacing * tangent, Color.yellow);
  //     if (legCenter == null)
  //     {
  //       legCenter = hit.point;
  //     }
  //     else
  //     {
  //       legCenter = (legCenter + hit.point) / 2f;
  //     }
  //   }

  //   Vector3 baseHipPosition = legCenter.Value + Vector2.up * baseHipOffsetY;
  //   // calculate hip
  //   Gizmos.color = Color.yellow;
  //   Gizmos.DrawSphere(baseHipPosition, 0.1f);
  //   GizmosExt.DrawLine(baseHipPosition, baseHipPosition + 0.5f * hipSpacing * Vector3.up, Color.yellow);
  // }

  void CalculateStepCurve()
  {
    Keyframe key1 = new(0f, -1f)
    {
      inTangent = 0f,
      outTangent = 0f
    };

    Keyframe key2 = new(forwardPercent, 1f)
    {
      inTangent = 0f,
      outTangent = (-1f - 1f) / (1f - forwardPercent)
    };


    Keyframe key3 = new(1f, -1f)
    {
      inTangent = key2.outTangent,
      outTangent = 0f,
    };

    stepCurve.AddKey(key1);
    stepCurve.AddKey(key2);
    stepCurve.AddKey(key3);

    stepCurve.preWrapMode = WrapMode.Loop;
    stepCurve.postWrapMode = WrapMode.Loop;
  }

  void CalculateVerticalCurve()
  {
    Keyframe key1 = new(0f, 0f)
    {
      inTangent = 0f,
      outTangent = 0f
    };

    Keyframe key2 = new(forwardPercent / 2, 1f)
    {
      inTangent = 0f,
      outTangent = 0f
    };


    Keyframe key3 = new(forwardPercent, 0f)
    {
      inTangent = 0f,
      outTangent = 0f,
    };

    Keyframe key4 = new(1f, 0f)
    {
      inTangent = 0f,
      outTangent = 0f,
    };

    verticalMovingCurve.AddKey(key1);
    verticalMovingCurve.AddKey(key2);
    verticalMovingCurve.AddKey(key3);
    verticalMovingCurve.AddKey(key4);

    verticalMovingCurve.preWrapMode = WrapMode.Loop;
    verticalMovingCurve.postWrapMode = WrapMode.Loop;
  }

  void CalculateHipCurve()
  {
    Keyframe key1 = new(0f, 0f)
    {
      inTangent = 0f,
      outTangent = 0f
    };

    Keyframe key2 = new(0.3f, 1f)
    {
      inTangent = 0f,
      outTangent = 0f
    };


    Keyframe key3 = new(0.5f, 0f)
    {
      inTangent = 0f,
      outTangent = 0f,
    };

    Keyframe key4 = new(0.8f, 1f)
    {
      inTangent = 0f,
      outTangent = 0f,
    };

    Keyframe key5 = new(1f, 0f)
    {
      inTangent = 0f,
      outTangent = 0f,
    };

    hipCurve.AddKey(key1);
    hipCurve.AddKey(key2);
    hipCurve.AddKey(key3);
    hipCurve.AddKey(key4);
    hipCurve.AddKey(key5);

    hipCurve.preWrapMode = WrapMode.Loop;
    hipCurve.postWrapMode = WrapMode.Loop;
  }

  void Start()
  {
    StartCoroutine(Chase());
  }

  IEnumerator DoContinuous(float duration, DoAction action)
  {
    float time = 0f;
    while (time < duration)
    {
      var t = time / duration;
      action(t);
      yield return null;
      time += Time.deltaTime;
    }
  }

  void ResetLand()
  {
    foreach (var walkingLeg in walkingLegs)
    {
      walkingLeg.landOffset = null;
      walkingLeg.landPosition = null;
    }
  }

  IEnumerator Chase()
  {
    // for offset edge case,  back direction, but the diff of verticalOffset exist too.
    ResetLand();
    bool handleForwardPercent = false;
    Vector2? centerOfLegs = null;

    yield return StartCoroutine(DoContinuous(stepDuration, t =>
    {
      if (!handleForwardPercent && t >= forwardPercent)
      {
        handleForwardPercent = true;
        ResetLand();
      }
      // for (int i = 0; i < walkingLegs.Length; ++i)
      foreach(var leg in walkingLegs)
      {
        var offsetDistance = stepCurve.Evaluate(t + leg.offset) * stepSpacing * 0.5f;

        Vector2 castPosition = transform.TransformPoint(new Vector2(leg.centerLine, 100f));
        var rootHit = Physics2D.Raycast(castPosition, Vector2.down, 10000f, GameConfig.Instance.groundMask);
        if (rootHit.collider != null)
        {
          var tangent = Vector2.Perpendicular(rootHit.normal).normalized;
          tangent = FacingDirection.x > 0 ? -tangent : tangent;
          var position = castPosition + tangent * offsetDistance;
          var hit = Physics2D.Raycast(position, Vector2.down, 10000f, GameConfig.Instance.groundMask);
          if (hit.collider != null)
          {
            var localPosition = transform.InverseTransformPoint(hit.point);
            var verticalOffset = verticalMovingCurve.Evaluate(t + leg.offset) * stepHeight;
            localPosition.y += verticalOffset;
            leg.target.localPosition = localPosition;

            if (leg.landPosition.HasValue)
            {
              leg.landOffset = (Vector2)leg.target.localPosition - leg.landPosition.Value;
            }
            leg.landPosition = leg.target.localPosition;
          }
        }
      }

      // hip position
      if (!centerOfLegs.HasValue)
      {
        centerOfLegs = walkingLegs.Average(l => l.target.localPosition);
      }

      var hipPosition = centerOfLegs.Value + Vector2.up * baseHipOffsetY;
      hipPosition.x += hipOffsetX;
      hipPosition.y += hipCurve.Evaluate(t) * hipSpacing;
      hip.localPosition = hipPosition;

      // Move the body
      {
        Vector2 landOffset = Vector2.zero;
        foreach(var leg in walkingLegs)
        {
          if (leg.landOffset.HasValue)
          {
            if (Vector2.Dot(leg.landOffset.Value, FacingDirection) < 0)
            {
              landOffset = leg.landOffset.Value;
              break;
            }
          }
        }

        transform.position -= (Vector3)landOffset;
      }
    }));

    StartCoroutine(Chase());
  }
}