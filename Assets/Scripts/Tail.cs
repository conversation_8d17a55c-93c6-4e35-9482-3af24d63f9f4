
using UnityEngine;

public class Tail : MonoBehaviour {
  [SerializeField] int length;
  [SerializeField] Transform target;
  [SerializeField] float targetDist;
  [SerializeField] float smoothSpeed;
  [SerializeField] float moveSpeed;
  [SerializeField] float tailSpeed;
  [SerializeField] float wiggleSpeed;
  [SerializeField] float wiggleMagnitude;
  Vector3[] segmentPoses;
  Vector3[] segmentV;
  LineRenderer lineRenderer;



  void Start()
  {
    lineRenderer = GetComponent<LineRenderer>();
    lineRenderer.positionCount = length;
    segmentPoses = new Vector3[length];
    segmentV = new Vector3[length];
  }

  void Update()
  {
    target.localRotation = Quaternion.Euler(0, 0, Mathf.Sin(Time.time * wiggleSpeed) * wiggleMagnitude);

    segmentPoses[0] = target.position;

    for (int i = 1; i < segmentPoses.Length; ++i)
    {
      segmentPoses[i] = Vector3.SmoothDamp(segmentPoses[i], segmentPoses[i-1] + target.right * targetDist, ref segmentV[i], smoothSpeed);
      // segmentPoses[i] = Vector3.MoveTowards(segmentPoses[i], segmentPoses[i-1] + target.right * targetDist, moveSpeed * Time.deltaTime);
    }
    lineRenderer.SetPositions(segmentPoses);
  }

}