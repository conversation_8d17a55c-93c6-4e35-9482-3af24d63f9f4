
using UnityEngine;
using UnityEngine.Splines;

public class BeizierCurve
{
  private Vector3 P0;
  private Vector3 P1;
  private Vector3 P2;
  private Vector3 P3;

  public BeizierCurve((Vector3, Vector3, Vector3, Vector3) controlPoints)
  {
    P0 = controlPoints.Item1;    
    P1 = controlPoints.Item2;
    P2 = controlPoints.Item3;
    P3 = controlPoints.Item4;
  }

  public Vector3 Evaluate(float t)
  {
    // https://pomax.github.io/bezierinfo/#abc
    // P(t) = (1 - t)^3 * P0 + 3t(1-t)^2 * P1 + 3t^2 (1-t) * P2 + t^3 * P3
    var mt = 1 - t;

    var t2 = t * t;
    var t3 = t2 * t;
    var mt2 = mt * mt;
    var mt3 = mt2 * mt;

    return mt3 * P0 + 3 * t * mt2 * P1 + 3 * t2 * mt * P2 + t3 * P3;
  }

  public Vector3 EvaluateTangent(float t)
  {
    // dP(t) / dt =  -3(1-t)^2 * P0 + 3(1-t)^2 * P1 - 6t(1-t) * P1 - 3t^2 * P2 + 6t(1-t) * P2 + 3t^2 * P3 
    var mt = 1 - t;
    var t2 = t * t;
    var mt2 = mt * mt;

    return -3 * mt2 * P0 + 3 * mt2 * P1 - 6 * t * mt * P1 - 3 * t2 * P2 + 6 * t * mt * P2 + 3 * t2 * P3;
  }


}