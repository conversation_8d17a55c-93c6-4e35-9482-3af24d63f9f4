
using UnityEngine;

namespace GroundSnake
{
  public class IdleState : State
  {
    public IdleState(StateMachine stateMachine) : base(stateMachine)
    {
      // idleController = brain.GetComponent<IdleController>();
    }

    public override void Enter(State prevState)
    {
      base.Enter(prevState);
      if (prevState == stateMachine.attackState)
      {
        tween
          .ResumeTrack(t => t.lookDirection)
          .BeginBatchChange(new() { loopCount = -1, pingpong = true })
          .SetAnimTrack(f => f.anim, Tween.CreateAnimTrackClip(brain.anim_Idle, crossDuration: 0.5f))
          .EndBatchChange();
      }
      else
      {
        tween
          .SetSpeedTrack(t => t.lookDirection, Tween.CreateSpeedTrackClip(
            () =>
            {
              if (brain.target != null)
                return (Vector2)(brain.target.transform.position - brain.eye.position);
              return tween.DefaultValue.lookDirection;
            },
            () => brain.lookSpeed,
            () => (Vector2)brain.eye.right,
            interpolateFunc: Vector2Ext.SlerpTowards,
            compareFunc: Vector2Ext.AngleEqual
          ))
          // .SetSpeedTrack(t => t.lookDirection, Tween.CreateSpeedTrackChange(
          //   () => tween.DefaultValue.lookDirection, () => brain.lookSpeed, 
          //   interpolateFunc: Vector2Ext.SlerpTowards, 
          //   compareFunc: Vector2Ext.AngleEqual
          // ))
          .BeginBatchChange(new() { loopCount = -1, pingpong = true })
          .SetAnimTrack(f => f.anim, Tween.CreateAnimTrackClip(brain.anim_Idle))
          .EndBatchChange();
      }
    }
  }
}