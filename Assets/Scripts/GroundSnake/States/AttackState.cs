
using System.Collections;
using System.Threading.Tasks;
using UnityEngine;

namespace GroundSnake
{
  public class AttackState : State
  {
    private Vector3 aimPosition;

    private TweenClip eatClip;
    private TweenClip aimClip;
    private TweenClip mouthOpenClip;
    private TweenClip attackClip;
    private TweenClip attackMouthOpenClip;

    private Coroutine aimCoroutine;

    public AttackState(StateMachine stateMachine) : base(stateMachine)
    {
      eatClip = Tween.CreateAnimTrackClip(stateMachine.brain.anim_eat, 1);
      aimClip = Tween.CreateAnimTrackClip(stateMachine.brain.anim_aim, 0, crossDuration: 0.5f);
      mouthOpenClip = Tween.CreateAnimTrackClip(stateMachine.brain.anim_mouthOpen, 1, crossDuration: 0.5f);
      attackMouthOpenClip = Tween.CreateAnimTrackClip(stateMachine.brain.anim_mouthOpenMax, 1);
    }

    public override void Enter(State prevState)
    {
      base.Enter(prevState);
      aimCoroutine = brain.StartCoroutine(Aim());
    }

    public override void Exit()
    {
      base.Exit();
      brain.StopCoroutine(aimCoroutine);
    }

    IEnumerator Aim()
    {
      while (brain.IsInAttackArea)
      {
        tween
          .SetAnimTrack(t => t.anim, aimClip)
          .SetAnimTrack(t => t.anim, mouthOpenClip);

        yield return tween.WaitReachTarget(t => t.anim, t => t.lookDirection);

        aimPosition = brain.target.transform.position;
        var constrain = new ChainMoveConstrain(brain.transform, new() { effector = brain.mouth, chainLength = 3 });
        var (fromAnimData, toAnimData) = constrain.MoveTo(brain.target.transform.position);

        attackClip = Tween.CreateAnimTrackClip(toAnimData, 0, crossDuration: brain.anim_mouthOpenMax.duration);


        tween
          .PauseTrack(t => t.lookDirection)
          .SetAnimTrack(t => t.anim, Tween.CreateAnimTrackClip(fromAnimData, 0))
          .SetAnimTrack(t => t.anim, attackClip)
          .SetAnimTrack(t => t.anim, attackMouthOpenClip);


        yield return tween.WaitReachTarget(t => t.anim);

        tween
          .SetAnimTrack(t => t.anim, Tween.CreateAnimTrackClip(fromAnimData, 0, crossDuration: 1.5f));

        yield return tween.WaitReachTarget(t => t.anim);

        tween
          .ResumeTrack(t => t.lookDirection);
      }

      stateMachine.TransitionTo(stateMachine.idleState);
    }

    public override void FrameUpdate()
    {
      var track0 = tween.GetSubAnimTrack(t => t.anim, 0);
      var track1 = tween.GetSubAnimTrack(t => t.anim, 1);

      if (track0?.currentClip.hash == attackClip?.hash && track1?.currentClip.hash == attackMouthOpenClip.hash)
      {
        if (Vector2.SqrMagnitude(aimPosition - brain.mouth.position) < brain.eatDetectSqrDistance)
        {
          tween.SetAnimTrack(t => t.anim, eatClip);
        }
      }
    }
  }
}