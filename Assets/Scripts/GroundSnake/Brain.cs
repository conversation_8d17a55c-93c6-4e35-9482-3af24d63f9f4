
using UnityEditor;
using UnityEngine;

namespace GroundSnake
{
  public class Brain : BaseBrain<Brain, StateMachine, State>
  {
    public struct Feature
    {
      public AnimFeature anim;
      public Vector2 lookDirection;


      public Quaternion headRotation;
    }

    [Header("Animations")]
    public AnimData anim_Idle;
    public AnimData anim_aim;
    public AnimData anim_mouthOpen;
    public AnimData anim_mouthOpenMax;
    public AnimData anim_eat;

    [Header("Transforms")]
    public Transform root;
    public Transform neck;
    public Transform head;
    public Transform eye;
    public Transform mouth;

    [Header("Features")]
    public float lookSpeed;
    public float eatDetectSqrDistance;
    public float attackRate;

    public float widthMultiplier;
    public float rootTangent;
    public float neckTangent;
    public int bodyPrecision = 20;

    public bool IsInAttackArea { get; private set; } = false;
    private LineRenderer _lineRenderer;

    public TweenAnim<Feature> tween { private set; get; }
    public Transform target { private set; get; }
    private (Vector3, Vector3, Vector3, Vector3) beizierControlPoints => (
      root.position, root.position + root.right * rootTangent, neck.position - neck.right * neckTangent, neck.position
    );

    void OnDrawGizmos()
    {
      Gizmos.color = Color.green;
      Gizmos.DrawSphere(root.position, 0.2f);

      var startTangent = root.position + root.right * rootTangent;
      var endTangent = neck.position - neck.right * neckTangent;

      Handles.DrawDottedLine(root.position, startTangent, rootTangent);
      Handles.DrawDottedLine(neck.position, endTangent, neckTangent);

      Handles.DrawBezier(root.position, neck.position, startTangent, endTangent, Color.red, null, 5);
    }

    protected override void Awake()
    {
      base.Awake();

      _lineRenderer = GetComponent<LineRenderer>();


      tween = gameObject.TweenAnim<Feature>(new()
      {
        lookDirection = eye.right,
        headRotation = head.rotation
      });
      StateMachine.Initialize(StateMachine.idleState);
    }

    void Start()
    {
      // statemachine. -> idle
      tween
        .SetOnChange(DoFeature)
        .Start();
    }

    void DoFeature(Feature f)
    {
      // anim
      transform.AnimFeature(f.anim);

      // look target
      if (tween.IsTrackRunning(t => t.lookDirection))
      {
        var rotation = Quaternion.FromToRotation(tween.DefaultValue.lookDirection, f.lookDirection);
        head.rotation = tween.DefaultValue.headRotation * rotation;
      }

      // Draw body
      _lineRenderer.widthMultiplier = widthMultiplier;
      _lineRenderer.SetBeizierCurve(bodyPrecision, beizierControlPoints);
    }

    public void OnEnterOrExitDisturb(GameObject gameObject, OverlapArea area)
    {
      // not aim
      var oldestStayObject = area.GetOldestStayObject();
      target = oldestStayObject != null ? oldestStayObject.transform : null;
    }

    public void OnEnterAttack(GameObject gameObject, OverlapArea area)
    {
      IsInAttackArea = true;
      var oldestStayObject = area.GetOldestStayObject();
      target = oldestStayObject != null ? oldestStayObject.transform : target;
      StateMachine.TransitionTo(StateMachine.attackState);
    }
    public void OnExitAttack(GameObject gameObject, OverlapArea area)
    {
      var oldestStayObject = area.GetOldestStayObject();
      target = oldestStayObject != null ? oldestStayObject.transform : target;
      if (oldestStayObject == null)
      {
        IsInAttackArea = false;
      }
    }
  }
}
