
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace GroundSnake
{

  public class StateMachine : BaseStateMachine<Brain, StateMachine, State>
  {

    public IdleState idleState;
    public AttackState attackState;

    public StateMachine(Brain brain) : base(brain)
    {
      idleState = new IdleState(this);
      attackState = new AttackState(this);
    }

  }

}
