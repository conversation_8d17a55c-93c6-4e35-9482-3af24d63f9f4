using System.Linq;
using UnityEngine;

public struct BonePoint
{
  public Transform transform;
  public Vector3 defaultPosition;
}

public class GroundSnakeController : MonoBehaviour
{
  [SerializeField] Transform headEffector;
  [SerializeField] int chainLength;
  [SerializeField] Transform target;

  BonePoint[] bonePoints;
  float[] lengths;
  // LineRenderer lineRenderer;

  void Start()
  {
    // lineRenderer = GetComponent<LineRenderer>();
    bonePoints = new BonePoint[chainLength];
    lengths = new float[chainLength - 1];
    var tr = headEffector;
    for (int i = 0; i < chainLength; i++)
    {
      bonePoints[i] = new BonePoint() {
        transform = tr,
        defaultPosition = tr.position
      };
      tr = tr.parent;
    }

    for (int i = 0; i < chainLength - 1; ++i)
    {
      lengths[i] = Vector3.Distance(bonePoints[i].defaultPosition, bonePoints[i + 1].defaultPosition);
    }
  }

  void Update()
  { 
    if (Input.GetKeyDown(KeyCode.Space))
    {
      ToTarget();
    }
  }

  void ToTarget()
  {
    // tail -> head position
    var positions = new Vector3[chainLength];
    positions[0] = target.position;
    for (int i = 1; i < chainLength; i++)
    {
      positions[i] = positions[i - 1] + (bonePoints[i].defaultPosition - positions[i - 1]).normalized * lengths[i - 1];
    }

    Debug.Log(string.Join(", ", positions));


    var constrain = new ChainMoveConstrain(transform, new () { effector = headEffector, chainLength = chainLength});
    constrain.MoveTo(target.position);


    // var last = chainLength - 1;
    // bonePoints[last].transform.position = positions[last];
    // for (int i = chainLength - 1; i >= 1; i--)
    // {
    //   bonePoints[i].transform.rotation = Quaternion.FromToRotation(Vector2.right, positions[i - 1] - positions[i]);
    // }

    // Debug.Log(string.Join(", ", bonePoints.Select(b => b.transform.localEulerAngles)));

  }
}
