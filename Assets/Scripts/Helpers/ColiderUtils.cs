using System.Collections;
using System.Collections.Generic;
using Unity.Collections;
using Unity.VisualScripting;
using UnityEditorInternal;
using UnityEngine;

public struct CastCircle
{
    public Vector2 position;
    public float radius;
}

public enum ColliderPosition
{
    Center,
    BottomLeft,
    BottomRight,
    BottomCenter,
    TopLeft,
    TopRight
}


public class ColliderUtils
{

    public static Vector2 GetCenterOfColider(Vector2 position, Vector2 lossyScale, Collider2D collider)
    {
        return position + collider.offset * lossyScale;
    }
    public static Vector2 GetCenterOfColider(GameObject gameObject)
    {
        var transform = gameObject.transform;
        var collider = gameObject.GetComponent<Collider2D>();
        return GetCenterOfColider(transform.position, transform.lossyScale, collider);
    }

    public static Vector2 GetPositionOf(GameObject gameObject, ColliderPosition position)
    {
        var transform = gameObject.transform;
        var collider = gameObject.GetComponent<Collider2D>();
        var center = GetCenterOfColider(transform.position, transform.lossyScale, collider);

        switch (position)
        {
            case ColliderPosition.Center:
                return center;
            case ColliderPosition.BottomCenter:
                return center + new Vector2(0, -collider.bounds.extents.y);
            case ColliderPosition.BottomLeft:
                return center + new Vector2(-collider.bounds.extents.x, -collider.bounds.extents.y);
            case ColliderPosition.BottomRight:
                return center + new Vector2(collider.bounds.extents.x, -collider.bounds.extents.y);
            case ColliderPosition.TopLeft:
                return center + new Vector2(-collider.bounds.extents.x, collider.bounds.extents.y);
            case ColliderPosition.TopRight:
                return center + new Vector2(collider.bounds.extents.x, collider.bounds.extents.y);
        }
        return Vector2.zero;
    }

    // public static Vector2 GeOfColider(GameObject gameObject)
    // {
    //     return GetCenterOfColider(transform.position, transform.lossyScale, collider);
    // }


    public static CastCircle GetBottomCenterCircle(Vector2 position, Vector2 lossyScale, Collider2D collider)
    {
        var centerOfColider = GetCenterOfColider(position, lossyScale, collider);
        return new CastCircle()
        {
            position = centerOfColider + new Vector2(0, -collider.bounds.extents.y),
            radius = collider.bounds.extents.x
        };
    }
    public static Vector2 GetBottomRightPosition(Vector2 position, Vector2 lossyScale, Collider2D collider)
    {
        var centerOfColider = GetCenterOfColider(position, lossyScale, collider);
        return centerOfColider + new Vector2(collider.bounds.extents.x, -collider.bounds.extents.y);
    }
    public static Vector2 GetBottomLeftPosition(Vector2 position, Vector2 lossyScale, Collider2D collider)
    {
        var centerOfColider = GetCenterOfColider(position, lossyScale, collider);
        return centerOfColider + new Vector2(-collider.bounds.extents.x, -collider.bounds.extents.y);
    }
}