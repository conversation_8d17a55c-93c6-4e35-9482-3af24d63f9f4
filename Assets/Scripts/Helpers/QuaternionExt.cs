using UnityEngine;

public static class QuaternionExt
{
  public static Quaternion Tangent(Vector2 from, Vector2 to)
  {
    var tangent = (to - from).normalized;
    return Tangent(tangent);
  }

  public static Quaternion Tangent(Vector2 tangent)
  {
    var angle = Vector2.SignedAngle(Vector2.right, tangent);
    return Quaternion.AngleAxis(angle, Vector3.forward);
  }

  public static Vector3 GetClosestEquivalentQuaternion(this Quaternion current, Quaternion from)
  {
    return new (
      MathfExt.GetClosestEquivalentAngle(from.eulerAngles.x, current.eulerAngles.x),
      MathfExt.GetClosestEquivalentAngle(from.eulerAngles.y, current.eulerAngles.y),
      MathfExt.GetClosestEquivalentAngle(from.eulerAngles.z, current.eulerAngles.z)
    );
    
  }
}