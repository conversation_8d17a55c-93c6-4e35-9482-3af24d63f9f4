
using System;
using UnityEngine;

public static class LineRendExt {
  public static void SetBeizierCurve(this LineRenderer lineRenderer, int precision, (Vector3, Vector3, Vector3, Vector3) controlPoints)
  {
    var points = BeizierCurve(precision, controlPoints);
    lineRenderer.positionCount = precision;
    lineRenderer.SetPositions(points);
  }

  public static Vector3[] BeizierCurve(int precision, (Vector3, Vector3, Vector3, Vector3) controlPoints)
  {
    var curve = new BeizierCurve(controlPoints);

    Vector3[] points = new Vector3[precision];
    for (int i = 0; i < precision; ++i)
    {
      var t = (float)i / (precision - 1);
      points[i] = curve.Evaluate(t);
    }
    return points;
  }

  public static Vector3[] MakeCurve(Vector2 startPosition, Vector2 endPosition, int precision, float multiplier = 1f, EaseType easeType = EaseType.Zero, Func<float, float> easeFunc = null, AnimationCurve curve = null)
  {
    var direction = endPosition - startPosition;
    return MakeCurve(startPosition, direction.normalized, direction.magnitude, precision, multiplier, easeType, easeFunc, curve);
  }

  public static Vector3[] MakeCurve(Vector2 startPosition, Vector2 dir, float length, int precision, float multiplier = 1f, EaseType easeType = EaseType.Zero, Func<float, float> easeFunc = null, AnimationCurve curve = null)
  {
    var points = new Vector3[precision];

    var maxY = TweenHelper.Ease(curve, easeFunc, easeType, 1f) * multiplier;
    var xLength = length * length;
    if (Mathf.Abs(length) >= Mathf.Abs(maxY))
    {
      xLength = Mathf.Sqrt(length * length - maxY * maxY);
    }

    for (int i = 0; i < precision; ++i)
    {
      var x = i * xLength / (precision - 1);
      var y = TweenHelper.Ease(curve, easeFunc, easeType, x / xLength) * multiplier;
      points[i] = new Vector2(x, y);
    }

    var actualDir = points[precision - 1] - points[0];

    Quaternion rotation = Quaternion.FromToRotation(actualDir, dir);
    // Quaternion rotation = Quaternion.Euler(0, 0, 0);
    for (int i = 0; i < precision; ++i)
    {
      points[i] = (Vector3)startPosition + rotation * points[i];
    }
    return points;
  }
}