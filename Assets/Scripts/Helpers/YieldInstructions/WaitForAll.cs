
using UnityEngine;

public class WaitForAll : CustomYieldInstruction
{
  private CustomYieldInstruction[] instructions;

  public WaitForAll(params CustomYieldInstruction[] instructions)
  {
    this.instructions = instructions;
  }

  public override bool keepWaiting
  {
    get
    {
      foreach (var instruction in instructions)
      {
        if (instruction.keepWaiting)
        {
          return true;
        }
      }
      return false;
    }
  }
}
