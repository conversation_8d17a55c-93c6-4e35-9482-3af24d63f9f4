
using UnityEngine;

public static class Vector2Ext {
  public static Vector2 Slerp(Vector2 dir1, Vector2 dir2, float t)
  {
    var angle1 = MathfExt.DirectionToAngle(dir1);
    var angle2 = MathfExt.DirectionToAngle(dir2);

    var angle = Mathf.LerpAngle(angle1, angle2, t);
    return MathfExt.AngleToDirection(angle);
  }
  public static Vector2 SlerpTowards(Vector2 dir1, Vector2 dir2, float speed)
  {
    var angle1 = MathfExt.DirectionToAngle(dir1);
    var angle2 = MathfExt.DirectionToAngle(dir2);


    var angle = Mathf.MoveTowardsAngle(angle1, angle2, speed * Time.deltaTime);
    return MathfExt.AngleToDirection(angle);
  }

  public static bool AngleEqual(Vector2 dir1, Vector2 dir2)
  {
    return Mathf.Approximately(MathfExt.DirectionToAngle(dir1), MathfExt.DirectionToAngle(dir2));
  }
}