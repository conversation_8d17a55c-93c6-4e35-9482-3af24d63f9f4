

using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

public static class AnimHelper
{
  public static AnimData.Binding MakeAnimTransformBinding(Transform root, Transform descendant, string propertyName, Keyframe[] keyframes)
  {
    return new AnimData.Binding() {
      path = GetRelativePath(root, descendant),
      propertyName = propertyName,
      typeName = "Transform",
      curve = new AnimationCurve(keyframes),
    };
  }
  public static AnimData.Binding[] MakeAnimTransformBinding(Transform root, Transform descendant, string baseName, Vector3[] vectors, float[] times)
  {
    if (vectors.Length != times.Length)
      return default;

    var bindings = new AnimData.Binding[3];

    Func<float[], Keyframe[]> func = (float[] values) => {
      var keyframes = new Keyframe[times.Length];
      for (int i = 0; i < times.Length; ++i)
      {
        keyframes[i] = new Keyframe(times[i], values[i]);
      }
      return keyframes;
    };

    return new [] {
      MakeAnimTransformBinding(root, descendant, $"{baseName}.x", func(vectors.Select(p => p.x).ToArray())),
      MakeAnimTransformBinding(root, descendant, $"{baseName}.y", func(vectors.Select(p => p.y).ToArray())),
      MakeAnimTransformBinding(root, descendant, $"{baseName}.z", func(vectors.Select(p => p.z).ToArray())),
    };
  }
  public static AnimData.Binding[] MakeAnimPositionBinding(Transform root, Transform descendant, Vector3[] positions, float[] times)
  {
    return MakeAnimTransformBinding(root, descendant, "m_LocalPosition", positions, times);
  }

  public static AnimData.Binding[] MakeAnimPositionBinding(Transform root, Transform descendant, Vector3 position)
  {
    return MakeAnimTransformBinding(root, descendant, "m_LocalPosition", new Vector3[] { position }, new float[] { 0 });
  }
  public static AnimData.Binding[] MakeAnimRotationBinding(Transform root, Transform descendant, Vector3[] rotations, float[] times)
  {
    return MakeAnimTransformBinding(root, descendant, "localEulerAnglesRaw", rotations, times);
  }

  public static AnimData.Binding[] MakeAnimRotationBinding(Transform root, Transform descendant, Vector3 rotation)
  {
    return MakeAnimTransformBinding(root, descendant, "localEulerAnglesRaw", new Vector3[] { rotation }, new float[] { 0 });
  }

  public static string GetRelativePath(Transform root, Transform descendant)
  {
    if (root == null || descendant == null)
      return string.Empty;

    List<string> path = new List<string>();
    Transform current = descendant;

    while (current != null && current != root)
    {
      path.Add(current.gameObject.name);
      current = current.parent;
    }

    if (current != root)
      return string.Empty; // descendant is not actually a child of root

    path.Reverse();
    return string.Join("/", path);
  }
  public static void AnimFeature(this Transform transform, AnimFeature feature)
  {
    var features = feature.features;

    if (features == null || features.Length == 0)
      return;

    var groups = features.GroupBy(f => $"{f.path}:{f.typeName}");
    foreach (var group in groups)
    {
      string[] parts = group.Key.Split(new[] { ':' }, 2);
      GameObject o = transform.Find(parts[0]).gameObject;
      var t = o.GetComponent(parts[1]);

      Dictionary<string, float> pairs = new();
      foreach (var f in group)
      {
        pairs.Add(f.propertyName, f.value);
      }

      if (t is Transform tr)
      {
        SetValue(tr, pairs);
      }
    }
  }

  private static void SetValue(Transform transform, Dictionary<string, float> pairs)
  {
    if (pairs.ContainsKey("m_LocalPosition.x") || pairs.ContainsKey("m_LocalPosition.y") || pairs.ContainsKey("m_LocalPosition.z"))
    {
      Vector3 position = transform.localPosition;
      if (pairs.ContainsKey("m_LocalPosition.x"))
      {
        position.x = pairs["m_LocalPosition.x"];
      }
      if (pairs.ContainsKey("m_LocalPosition.y"))
      {
        position.y = pairs["m_LocalPosition.y"];
      }
      if (pairs.ContainsKey("m_LocalPosition.z"))
      {
        position.z = pairs["m_LocalPosition.z"];
      }

      transform.localPosition = position;
    }

    if (pairs.ContainsKey("localEulerAnglesRaw.x") || pairs.ContainsKey("localEulerAnglesRaw.y") || pairs.ContainsKey("localEulerAnglesRaw.z"))
    {
      Vector3 eulerAngles = transform.localEulerAngles;
      if (pairs.ContainsKey("localEulerAnglesRaw.x"))
      {
        eulerAngles.x = pairs["localEulerAnglesRaw.x"];
      }
      if (pairs.ContainsKey("localEulerAnglesRaw.y"))
      {
        eulerAngles.y = pairs["localEulerAnglesRaw.y"];
      }
      if (pairs.ContainsKey("localEulerAnglesRaw.z"))
      {
        eulerAngles.z = pairs["localEulerAnglesRaw.z"];
      }
      transform.localRotation = Quaternion.Euler(eulerAngles);
    }

    if (pairs.ContainsKey("m_LocalScale.x") || pairs.ContainsKey("m_LocalScale.y") || pairs.ContainsKey("m_LocalScale.z"))
    {
      Vector3 scale = transform.localScale;
      if (pairs.ContainsKey("m_LocalScale.x"))
      {
        scale.x = pairs["m_LocalScale.x"];
      }
      if (pairs.ContainsKey("m_LocalScale.y"))
      {
        scale.y = pairs["m_LocalScale.y"];
      }
      if (pairs.ContainsKey("m_LocalScale.z"))
      {
        scale.z = pairs["m_LocalScale.z"];
      }
      transform.localScale = scale;
    }

  }

}
