using System.Collections;
using System.Collections.Generic;
using Unity.Collections;
using Unity.VisualScripting;
using UnityEditorInternal;
using UnityEngine;

public class DebugExt
{
  public static void DrawDebugCircle(Vector3 position, float radius, Color color, float duration = 0f)
  {
    int segments = 20;
    float angle = 0f;
    for (int i = 0; i < segments; i++)
    {
      float x = Mathf.Cos(angle) * radius;
      float y = Mathf.Sin(angle) * radius;
      Vector3 start = new Vector3(x, y, 0) + position;
      angle += 2 * Mathf.PI / segments;
      x = Mathf.Cos(angle) * radius;
      y = Mathf.Sin(angle) * radius;
      Vector3 end = new Vector3(x, y, 0) + position;
      Debug.DrawLine(start, end, color, duration);
    }
  }
}