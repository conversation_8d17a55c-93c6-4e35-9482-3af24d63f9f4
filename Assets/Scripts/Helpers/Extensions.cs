using UnityEngine;
using System.Collections.Generic;
using System;

  public struct PositionAndTangent
  {
    public Vector3 position;
    public Vector3 tangent;
  }


public static class EnumerableVector3Extensions
{
    public static void SetPositionAndTangent(this Transform transform, PositionAndTangent positionAndTangent)
    {
        transform.position = positionAndTangent.position;

        var angle = Vector2.SignedAngle(Vector2.right, positionAndTangent.tangent);
        transform.rotation = Quaternion.AngleAxis(angle, Vector3.forward);
    }

    public static Vector3 Average<T>(this IEnumerable<T> source, Func<T, Vector3> selector)
    {
        if (source == null)
            throw new ArgumentNullException(nameof(source));
        
        if (selector == null)
            throw new ArgumentNullException(nameof(selector));

        Vector3 sum = Vector3.zero;
        int count = 0;

        foreach (var item in source)
        {
            sum += selector(item);
            count++;
        }

        if (count == 0)
            return Vector3.zero;

        return sum / count;
    }

    public static float MinBy<T>(this IEnumerable<T> source, Func<T, float> selector)
    {
        if (source == null)
            throw new ArgumentNullException(nameof(source));
        
        if (selector == null)
            throw new ArgumentNullException(nameof(selector));

        float min = Mathf.NegativeInfinity;

        foreach (var item in source)
        {
            var value = selector(item);
            min = Mathf.Min(min, value);
        }

        return min;
    }

    public static float MaxBy<T>(this IEnumerable<T> source, Func<T, float> selector)
    {
        if (source == null)
            throw new ArgumentNullException(nameof(source));
        
        if (selector == null)
            throw new ArgumentNullException(nameof(selector));

        float max = Mathf.NegativeInfinity;

        foreach (var item in source)
        {
            var value = selector(item);
            max = Mathf.Max(max, value);
        }

        return max;
    }

}
