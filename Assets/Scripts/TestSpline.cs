


using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.Splines;

public class TestSpline : MonoBehaviour
{
  [SerializeField] SplineContainer splineContainer;
  [SerializeField] float t;

  void Awake()
  {
  }

  void OnDrawGizmos()
  {
    // position
    Vector3 position = splineContainer.EvaluatePosition(t);
    Gizmos.DrawSphere(position, 0.2f);
    // tangent
    Vector3 tangent = splineContainer.EvaluateTangent(t);
    Gizmos.DrawLine(position, position + tangent.normalized);
  }

  void FixedUpdate()
  {
  }
}
