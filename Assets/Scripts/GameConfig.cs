using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public enum GroundCheckMethod
{
    Raycast,
    BoxCast,
    CircleCast,
}

public class GameConfig : MonoBehaviour
{
    public float groundDistance = 0.5f;
    [Header("layer mask")]
    public LayerMask groundMask;
    public LayerMask wallMask;

    public PhysicsMaterial2D NoFriction;
    public static GameConfig Instance;


    void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
        }
    }

    void Start()
    {
        
    }

    // Update is called once per frame
    void Update()
    {
        
    }
}
