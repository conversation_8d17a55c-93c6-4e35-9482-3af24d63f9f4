
using System;
using System.Linq;
using UnityEngine;

public enum CurveType
{
  Position,
  Euler
}

public class AnimData : ScriptableObject
{

  [System.Serializable]
  public class Binding
  {
    public string path;
    public string typeName;
    public string propertyName;
    public AnimationCurve curve;
    public CurveType curveType
    {
      get
      {
        if (propertyName.Contains("localEulerAnglesRaw"))
          return CurveType.Euler;
        return CurveType.Position;
      }
    }

    public float duration
    {
      get
      {
        if (curve == null) return 0;
        return curve[curve.length - 1].time;
      }
    }

    public override bool Equals(object obj)
    {
      if (obj is Binding other)
      {
        return path == other.path &&
               typeName == other.typeName &&
               propertyName == other.propertyName &&
               curve.Equals(other.curve);
      }
      return false;
    }

    public override int GetHashCode()
    {
      return HashCode.Combine(path, typeName, propertyName, curve);
    }
  }

  [SerializeField]
  public Binding[] bindings;

  public float duration => bindings.MaxBy(b => b.duration);
  public int hash => GetHashCode();

  // public bool Equals(AnimData other)
  // {
  //   if (other == null || bindings == null || other.bindings == null)
  //     return false;

  //   if (bindings.Length != other.bindings.Length)
  //     return false;

  //   return bindings.SequenceEqual(other.bindings);
  // }
  // public override bool Equals(object obj)
  //   => obj is AnimData other && Equals(other);
  public override int GetHashCode()
  {
    if (bindings == null)
      return 0;

    // Combine hash codes of all bindings
    return bindings.Aggregate(0, (hash, binding) => HashCode.Combine(hash, binding.GetHashCode()));
  }

}


[System.Serializable]
public struct AnimFeature
{
  [System.Serializable]
  public struct SingleFeature
  {
    public string path;
    public string typeName;
    public string propertyName;
    public float value;

    public readonly string Key => $"{path}:{typeName}:{propertyName}";
  }

  public SingleFeature[] features;

  public bool Equals(AnimFeature other)
  {
    if (features == null || other.features == null)
      return features == other.features;

    return features.SequenceEqual(other.features);
  }

  public override bool Equals(object obj)
    => obj is AnimFeature other && Equals(other);

  public override int GetHashCode()
  {
    if (features == null)
      return 0;

    // Combine hash codes of all features
    return features.Aggregate(0, (hash, feature) => HashCode.Combine(hash, feature.GetHashCode()));
  }
}