using UnityEngine;


public class ButterflyController : MonoBehaviour
{
  [SerializeField] Transform target;
  [SerializeField] Vector2 speedRange = new(1f, 2f);
  [SerializeField] Vector2 frequencyRange = new(0f, 2f);
  [SerializeField] Vector2 normalOffsetRange = new(-2f, 2f);

  private Vector2 currentTarget;


  void Start()
  {
    TweenToTarget();
  }

  void Update()
  {

  }

  void TweenToTarget()
  {
    GetNextTarget();
    transform.TweenTowardsPosition(currentTarget, Random.Range(speedRange.x, speedRange.y), false)
      .SetOnComplete(() =>
      {
        TweenToTarget();
      })
      .Start();
  }

  void GetNextTarget()
  {
    var direction = target.position - transform.position;
    var lengthToTarget = direction.magnitude;

    var dir = direction.normalized;
    var frequency = Random.Range(frequencyRange.x, frequencyRange.y);

    var frequencyOffset = lengthToTarget;
    // TODO
    if (!Mathf.Approximately(frequency, 0))
    {
      frequencyOffset = Mathf.Min(1f / frequency, lengthToTarget);
    }

    if (Mathf.Approximately(frequencyOffset, lengthToTarget))
    {
      currentTarget = target.position;
    }
    else
    {
      var keyPosition = transform.position + dir * frequencyOffset;
      var normal = Vector2.Perpendicular(dir);

      currentTarget = (Vector2)keyPosition + normal * Random.Range(normalOffsetRange.x, normalOffsetRange.y);
    }
  }
}