using System.Collections.Generic;
using UnityEngine.Scripting.APIUpdating;

namespace UnityEngine.U2D.IK
{
    /// <summary>
    /// Utility for 2D Forward And Backward Reaching Inverse Kinematics (FABRIK) IK Solver.
    /// </summary>
    public static class FABRIKRotation2D
    {
        /// <summary>
        /// Solve IK based on FABRIK
        /// </summary>
        /// <param name="targetPosition">Target position.</param>
        /// <param name="solverLimit">Solver iteration count.</param>
        /// <param name="tolerance">Target position's tolerance.</param>
        /// <param name="lengths">Length of the chains.</param>
        /// <param name="positions">Chain positions.</param>
        /// <returns>Returns true if solver successfully completes within iteration limit. False otherwise.</returns>
        public static bool Solve(Vector2 targetPosition, int solverLimit, float tolerance, float[] lengths, Vector2 rootDir, Vector2[] constainRotations, ref Vector2[] positions)
        {
            var last = positions.Length - 1;
            var iterations = 0;
            var sqrTolerance = tolerance * tolerance;
            var sqrDistanceToTarget = (targetPosition - positions[last]).sqrMagnitude;
            var originPosition = positions[0];
            while (sqrDistanceToTarget > sqrTolerance)
            {
                ForwardRotation(targetPosition, lengths, rootDir, constainRotations, ref positions);
                BackwardRotation(originPosition, lengths, rootDir, constainRotations, ref positions);
                sqrDistanceToTarget = (targetPosition - positions[last]).sqrMagnitude;
                if (++iterations >= solverLimit)
                    break;
            }

            // Return whether positions have changed
            return iterations != 0;
        }

        static void ForwardRotation(Vector2 targetPosition, IList<float> lengths, Vector2 rootDir, Vector2[] constainRotations, ref Vector2[] positions)
        {
            // Debug.Log($"target: {targetPosition}");
            var last = positions.Length - 1;
            positions[last] = targetPosition;
            for (var i = last - 1; i >= 0; i--)
            {
              var boneDirection = i == 0 ? rootDir : (positions[i] - positions[i - 1]).normalized;
              var direction = positions[i + 1] - positions[i];
              var angle = Vector2.SignedAngle(boneDirection, direction);
              var clampedAngle = MathfExt.ClampAngle(angle, constainRotations[i].x, constainRotations[i].y);
              Vector2 dir = Quaternion.AngleAxis(clampedAngle, Vector3.forward) * boneDirection;
              positions[i] = positions[i + 1] + -dir * lengths[i];
            }
        }

        static void BackwardRotation(Vector2 originPosition, IList<float> lengths, Vector2 rootDir, Vector2[] constrainRotations, ref Vector2[] positions)
        {
          positions[0] = originPosition;
          for (int i = 0; i < positions.Length - 1; i++)
          {
            var boneDirection = i == 0 ? rootDir : (positions[i] - positions[i - 1]).normalized;
            var direction = positions[i + 1] - positions[i];
            var angle = Vector2.SignedAngle(boneDirection, direction);
            var clampedAngle = MathfExt.ClampAngle(angle, constrainRotations[i].x, constrainRotations[i].y);
            Vector2 dir = Quaternion.AngleAxis(clampedAngle, Vector3.forward) * boneDirection;
            positions[i + 1] = positions[i] + dir * lengths[i];
          }
        }

        static void Forward(Vector2 targetPosition, IList<float> lengths, ref Vector2[] positions)
        {
            var last = positions.Length - 1;
            positions[last] = targetPosition;
            for (var i = last - 1; i >= 0; --i)
            {
                var r = positions[i + 1] - positions[i];
                var l = lengths[i] / r.magnitude;
                var position = (1f - l) * positions[i + 1] + l * positions[i];
                positions[i] = position;
            }
        }

        static void Backward(Vector2 originPosition, IList<float> lengths, ref Vector2[] positions)
        {
            positions[0] = originPosition;
            var last = positions.Length - 1;
            for (var i = 0; i < last; ++i)
            {
                var r = positions[i + 1] - positions[i];
                var l = lengths[i] / r.magnitude;
                var position = (1f - l) * positions[i] + l * positions[i + 1];
                positions[i + 1] = position;
            }
        }

    }
}