
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Headless
{

  public class StateMachine : CharacterStateMachine<Brain, StateMachine, State>
  {

    public IdleState idleState;
    public ChaseState chaseState;
    public AttackState attackState;

    public StateMachine(Brain brain) : base(brain)
    {
      idleState = new IdleState(this);
      chaseState = new ChaseState(this);
      attackState = new AttackState(this);
    }

  }

}
