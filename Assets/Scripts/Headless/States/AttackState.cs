
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Headless
{
  public class AttackState : State
  {
    public AttackState(StateMachine stateMachine) : base(stateMachine)
    {
    }

    public override HashSet<BaseActionController<Brain, StateMachine, State>> InitializeControllers()
    {
      return new HashSet<BaseActionController<Brain, StateMachine, State>>
      {
      };
    }

    public override void Enter(State prevState)
    {
      base.Enter(prevState);
      DoAttack();
      // brain.PAnimator.completed +=
    }

    public void DoAttack()
    {
      brain.PAnimator.SetAttackTarget(brain.ChaseTarget);
      brain.PAnimator.Play(HeadlessState.Attack);
    }

    // void OnAnimationCompleted(HeadlessState state)
    // {
    //   if (state == HeadlessState.Attack)
    //   {

    //     brain.PAnimator.Play(HeadlessState.Attack);
    //   }
    // }
    // public override void Exit()
    // {
    //   base.Exit();
      
    // }

  }
}