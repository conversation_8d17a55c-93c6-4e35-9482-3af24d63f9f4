
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Headless
{
  public class ChaseState : State
  {
    private readonly int chaseHash = Animator.StringToHash("Chase");
    private ChaseController chaseController;
    public ChaseState(StateMachine stateMachine) : base(stateMachine)
    {
      chaseController = brain.GetComponent<ChaseController>();
    }

    public override HashSet<BaseActionController<Brain, StateMachine, State>> InitializeControllers()
    {
      return new HashSet<BaseActionController<Brain, StateMachine, State>>
          {
            chaseController
          };
    }

    public override void Enter(State prevState)
    {
      base.Enter(prevState);
      DoChase();
      // animator.CrossFade(chaseHash, 0.1f);
      // chaseController.DoChase();
    }

    public void DoChase()
    {
      var dir = (brain.ChaseTarget.transform.position - brain.transform.position).normalized;
      brain.UpdateDirection(dir.x < 0 ? CharacterDirection.Left : CharacterDirection.Right);
      brain.PAnimator.Play(HeadlessState.Chase);
    }
  }
}