
using UnityEngine.Splines;
using UnityEngine;

namespace Headless
{
  public class Brain : AttackBrain<Brain, StateMachine, State>, IDisturbable
  {
    [HideInInspector] public Vector2 Origin { private set; get; }
    [HideInInspector] public LegWalkController PAnimator;
    // public bool eyeOpen { private set; get; } = false;

    public Transform ChaseTarget { private set; get; }


    protected override bool FaceToRight => false;

    protected override void Awake()
    {
      base.Awake();
      PAnimator = GetComponent<LegWalkController>();
    }

    void Start()
    {
      StateMachine = new StateMachine(this);
      StateMachine.Initialize(StateMachine.idleState);

      Origin = transform.position;
    }

    protected override void Update()
    {
      base.Update();
    }

    public void DisturbBy(IDisturber disturber, int level)
    {
      // ChaseTarget = disturber.GetDisturbTarget(DisturbableType.Headless);
      // PAnimator.SetAttackTarget(ChaseTarget);
        ChaseTarget = disturber.GetDisturbTarget(DisturbableType.Headless);


      if (level == 0)
      {
        if (StateMachine.CurrentState == StateMachine.idleState)
        {
          StateMachine.TransitionTo(StateMachine.chaseState);
        }
      }
      else
      {
        StateMachine.TransitionTo(StateMachine.attackState);
      }
    }

    public void LoseDisturb(int level)
    {
      if (level == 1)
      {
        StateMachine.TransitionTo(StateMachine.chaseState);
      }
    }

  }

  public enum FlyPosition
  {
    InAir,
    InGround,
  }

}