

using System.Collections;
using UnityEngine;

namespace Headless
{
  public class ChaseController : ActionController
  {
    [SerializeField] float moveSpeed = 15;

    public void DoChase()
    {
      var dir = (brain.ChaseTarget.transform.position - transform.position).normalized;
      brain.UpdateDirection(dir.x < 0 ? CharacterDirection.Left : CharacterDirection.Right);
      controllerRigidbody.velocity = brain.FacingDirection * moveSpeed;
    }

    public void FixedUpdate()
    {
      var dir = (brain.ChaseTarget.transform.position - transform.position).normalized;
      // brain.UpdateDirection(dir.x < 0 ? CharacterDirection.Left : CharacterDirection.Right);
      // controllerRigidbody.velocity = brain.FacingDirection * moveSpeed;
      if (Vector2.Dot(dir, brain.FacingDirection) < 0)
      {
        brain.PAnimator.Play(HeadlessState.Idle);
      }
    }


    public void Exit()
    {
    }

  }

}