using System.Collections;
using System.Collections.Generic;
using Unity.Collections;
using Unity.VisualScripting;
using UnityEditorInternal;
using UnityEngine;

public class GamePhysics
{
    public static bool DetectGround(Vector2 position, Vector2 lossyScale, Collider2D collider)
    {
        var circle = ColliderUtils.GetBottomCenterCircle(position, lossyScale, collider);
        return Physics2D.OverlapCircle(circle.position, circle.radius, GameConfig.Instance.groundMask);
    }

    public static bool DetectGround(Vector2 position, GameObject gameObject)
    {
        var lossyScale = gameObject.transform.lossyScale;
        var collider = gameObject.GetComponent<Collider2D>();

        return DetectGround(position, lossyScale, collider);
    }
}