
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;

namespace Eye
{
  public class StareState : State
  {
    public readonly int idleHash = Animator.StringToHash("EyeFIdle");
    public readonly int eyeOpenHash = Animator.StringToHash("EyeFOpen");
    public readonly int eyeCloseHash = Animator.StringToHash("EyeFClose");
    private StareController stareController;
    public StareState(StateMachine stateMachine) : base(stateMachine)
    {
      stareController = brain.GetComponent<StareController>();
    }

    public override HashSet<BaseActionController<Brain, StateMachine, State>> InitializeControllers()
    {
      return new HashSet<BaseActionController<Brain, StateMachine, State>>
          {
            stareController,
          };
    }

    public override void OnAnimFinish(int hash)
    {
      if (hash == eyeCloseHash)
      {
        brain.Animator.Play(eyeOpenHash);
      }
      else if (hash == eyeOpenHash)
      {
        DoEyeClose();
      }
    }

    public override void Enter(State prevState)
    {
      base.Enter(prevState);
      DoEyeOpen(Random.Range(0, (int)stareController.EyeOpenDuration.x));
    }

    async void DoEyeClose()
    {
      await Task.Delay(Random.Range((int)stareController.EyeOpenDuration.x, (int)stareController.EyeOpenDuration.y));
      brain.Animator.Play(eyeCloseHash);
    }

    async void DoEyeOpen(int delay)
    {
      delay = delay == 0 ? Random.Range((int)stareController.EyeOpenDuration.x, (int)stareController.EyeOpenDuration.y) : delay;
      await Task.Delay(delay);
      brain.Animator.Play(eyeOpenHash);
    }
  }
}