
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;

namespace Eye
{
  public class IdleState : State
  {
    public readonly int eyeOpenHash = Animator.StringToHash("EyeFOpen");
    public readonly int eyeCloseHash = Animator.StringToHash("EyeFClose");
    public IdleState(StateMachine stateMachine) : base(stateMachine)
    {
    }

    public override HashSet<BaseActionController<Brain, StateMachine, State>> InitializeControllers()
    {
      return new HashSet<BaseActionController<Brain, StateMachine, State>>
          {
          };
    }

    public override void OnAnimFinish(int hash)
    {
      if (hash == eyeOpenHash)
      {
        DoEyeClose();
      }
    }

    async void DoEyeClose()
    {
      await Task.Delay(1000);
      brain.Animator.Play(eyeCloseHash);
    }
  }
}