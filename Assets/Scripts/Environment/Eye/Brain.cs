
using UnityEngine.Splines;
using UnityEngine;

namespace Eye
{
  public class Brain : CharacterBrain<Brain, StateMachine, State>, IDisturbable
  {
    [HideInInspector] public Vector2 Origin { private set; get; }
    // public bool eyeOpen { private set; get; } = false;
    [SerializeField] Animator eyelidAnimator;
    [SerializeField] float disturbRadius;
    public Transform StareTarget { private set; get; }

    protected override bool FaceToRight => false;

    protected override void Awake()
    {
      base.Awake();
      Animator = eyelidAnimator;
    }

    void Start()
    {
      StateMachine = new StateMachine(this);
      StateMachine.Initialize(StateMachine.idleState);

      Origin = transform.position;
    }

    protected override void Update()
    {
      base.Update();
    }

    public void DisturbBy(IDisturber disturber, int level)
    {
      StareTarget = disturber.GetDisturbTarget(DisturbableType.Eye);
      if (StateMachine.CurrentState == StateMachine.idleState)
      {
        StateMachine.TransitionTo(StateMachine.stareState);
      }
    }

    public void LoseDisturb(int level)
    {
      StateMachine.TransitionTo(StateMachine.idleState);
    }
  }

}