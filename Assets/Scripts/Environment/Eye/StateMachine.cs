
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Eye
{

  public class StateMachine : CharacterStateMachine<Brain, StateMachine, State>
  {

    public IdleState idleState;
    public StareState stareState;

    public StateMachine(Brain brain) : base(brain)
    {
      idleState = new IdleState(this);
      stareState = new StareState(this);
    }

  }

}
