


using UnityEngine;


public class FlyBugsController : MonoBehaviour
{
  struct ParticlePath
  {
    // degree
    public float currentDegree;
    public float nextDegree;
    public float degreeTime;

    // angularSpeed
    public float currentAngularSpeed;
    public float nextAngularSpeed;
    public float angularSpeedTime;

    // speed
    public float currentSpeed;
    public float nextSpeed;
    public float speedTime;

    // opacity
    public float currentOpacity;
    public float nextOpacity;
    public float opacityTime;
  }

  enum State
  {
    Gather,
    Disperse,
  }

  [SerializeField] ParticleSystem ps;
  [SerializeField] int count = 1;
  [SerializeField] float radius = 10f;
  [SerializeField] float radiusTolerance = 5f;
  [SerializeField] Vector2 speedRange;
  [SerializeField] float speedChangeSpeed = 1f;
  [SerializeField] Vector2 angularSpeedRange;
  [SerializeField] float angularChangeSpeed;
  [SerializeField] float fadeSpeed = 1f;
  [SerializeField] Vector2 gatherDelayRange;

  [SerializeField] LayerMask playerLayerMask;
  ParticleSystem.Particle[] particles;
  private ParticlePath[] paths;
  private State state = State.Gather;
  private float gatherDelay = 0f;
  private float gatherTime = 0f;

  void Awake()
  {
    var main = ps.main;
    main.maxParticles = count;
    main.startSpeed = 0f;

    var shape = ps.shape;
    shape.radius = radius;

    particles = new ParticleSystem.Particle[count];
    paths = new ParticlePath[count];

    for (int i = 0; i < count; i++)
    {
      CaclculateAngularSpeed(i, Random.Range(angularSpeedRange.x, angularSpeedRange.y));
      CalculateDegree(i, Random.Range(0f, Mathf.PI * 2f), ps.shape.position);
      CaclculateOpacity(i, 0f, ps.shape.position);
    }
    ps.Emit(count);
  }

  void Update()
  {
    UpdateState();
    Gather();
  }

  void UpdateState()
  {
    var collider = Physics2D.OverlapCircle(transform.position, ps.shape.radius, playerLayerMask);
    if (collider != null)
    {
      gatherTime = 0f;
      gatherDelay = Random.Range(gatherDelayRange.x, gatherDelayRange.y);
      state = State.Disperse;
    }
    else
    {
      if (gatherTime > gatherDelay)
      {
        state = State.Gather;
      }
      else
      {
        gatherTime += Time.deltaTime;
      }
    }
  }

  void Gather()
  {
    var num = ps.GetParticles(particles);

    for (var i = 0; i < num; i++)
    {
      // degree
      var degree = Mathf.Lerp(paths[i].currentDegree, paths[i].nextDegree, paths[i].degreeTime * paths[i].currentAngularSpeed);
      var dir = new Vector2(Mathf.Cos(degree * Mathf.Deg2Rad), Mathf.Sin(degree * Mathf.Deg2Rad));
      particles[i].velocity = dir * paths[i].currentSpeed;
      particles[i].rotation = degree;
      // Debug.Log($"dir: {paths[i].currentAngle:F0}::{paths[i].nextAngle:F0} --- {degree:F0}:{dir} --- {pa}");

      // update angle
      if (Mathf.Abs(paths[i].nextDegree - degree) < 0.01 || Vector2.Distance(particles[i].position, ps.shape.position) > radius + Random.Range(0f, radiusTolerance))
      {
        CalculateDegree(i, degree, particles[i].position);
        paths[i].degreeTime = 0f;
      }
      else
      {
        paths[i].degreeTime += Time.deltaTime;
      }

      // opacity
      var currentFadeSpeed = fadeSpeed;
      if (state == State.Disperse && paths[i].currentOpacity > 0.2f)
      {
        currentFadeSpeed *= 10f;
      }
      var opacity = Mathf.Lerp(paths[i].currentOpacity, paths[i].nextOpacity, paths[i].opacityTime * currentFadeSpeed);
      particles[i].startColor = new Color(0f, 0f, 0f, opacity);

      if (Mathf.Abs(paths[i].nextOpacity - opacity) < 0.01f)
      {
        CaclculateOpacity(i, opacity, particles[i].position);
        paths[i].opacityTime = 0f;
      }
      else
      {
        paths[i].opacityTime += Time.deltaTime;
      }

      // angular speed
      var angularSpeed = Mathf.Lerp(paths[i].currentAngularSpeed, paths[i].nextAngularSpeed, paths[i].angularSpeedTime * angularChangeSpeed);
      if (Mathf.Abs(paths[i].nextAngularSpeed - angularSpeed) < 0.01f)
      {
        CaclculateAngularSpeed(i, angularSpeed);
        paths[i].angularSpeedTime = 0f;
      }
      else
      {
        paths[i].angularSpeedTime += Time.deltaTime;
      }

      var speed = Mathf.Lerp(paths[i].currentSpeed, paths[i].nextSpeed, paths[i].speedTime * speedChangeSpeed);
      if (Mathf.Abs(paths[i].nextSpeed - speed) < 0.01f)
      {
        CaclculateSpeed(i, speed);
        paths[i].speedTime = 0f;
      }
      else
      {
        paths[i].speedTime += Time.deltaTime;
      }

    }

    ps.SetParticles(particles, num);
  }

  void CalculateDegree(int index, float currentAngle, Vector2 position)
  {
    var angle = currentAngle;
    if (state == State.Gather && Vector2.Distance(position, ps.shape.position) > radius + Random.Range(0f, radiusTolerance))
    {
      angle = Vector2.SignedAngle(Vector2.right, (Vector2)ps.shape.position - position);
      angle = Random.Range(angle - 10f, angle + 10f);
    }
    else if (state == State.Disperse)
    {
      var r = radius + radiusTolerance;
      if (Vector2.Distance(position, ps.shape.position) < r)
      {
        angle = Vector2.SignedAngle(Vector2.right, position - (Vector2)ps.shape.position);
        angle = Random.Range(angle - 10f, angle + 10f);
      }
      else if (Vector2.Distance(position, ps.shape.position) > r * 2f)
      {
        angle = Vector2.SignedAngle(Vector2.right, (Vector2)ps.shape.position - position);
        angle = Random.Range(angle - 10f, angle + 10f);
      }
    }

    paths[index].currentDegree = angle;
    var nextAngle = Random.Range(angle - 45f, angle + 45f);
    paths[index].nextDegree = nextAngle;
  }

  void CaclculateOpacity(int index, float currentOpacity, Vector2 position)
  {
    var nextOpacity = Random.Range(0.2f, 0.8f);
    var distancePercent = Vector2.Distance(position, ps.shape.position) / (radius + radiusTolerance);

    // (0-1) /  >1
    if (distancePercent > 1 || state == State.Disperse)
    {
      nextOpacity = Mathf.Pow(nextOpacity, distancePercent * 10f);
    }
    else
    {
      nextOpacity = Mathf.Pow(nextOpacity, distancePercent * 2f);
    }

    paths[index].currentOpacity = currentOpacity;
    paths[index].nextOpacity = nextOpacity;
  }

  void CaclculateAngularSpeed(int index, float currentAngularSpeed)
  {
    paths[index].currentAngularSpeed = currentAngularSpeed;
    paths[index].nextAngularSpeed = Random.Range(angularSpeedRange.x, angularSpeedRange.y);
  }

  void CaclculateSpeed(int index, float currentSpeed)
  {
    paths[index].currentSpeed = currentSpeed;
    paths[index].nextSpeed = Random.Range(speedRange.x, speedRange.y);
  }



}