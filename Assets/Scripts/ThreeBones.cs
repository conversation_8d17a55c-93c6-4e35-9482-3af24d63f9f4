
using UnityEngine;

[System.Serializable]
public class Bone
{
  public Transform transform;
  public float length;
  public float angle;
  public Color color;

  public Vector2 tailPosition {
    get {
      return transform.position + transform.right * length;
    }
  }

  public Vector2 globalDirection {
    get {
      return Quaternion.Euler(0, 0, angle) * Vector2.right;
    }
  }
}

[ExecuteInEditMode]
public class ThreeBones : MonoBehaviour
{
    public Bone first;
    public Bone second;
    public Bone third;

    public Transform target;

    public void SetRotation()
    {
      first.transform.localRotation = Quaternion.Euler(0, 0, first.angle);
      second.transform.localRotation = Quaternion.Euler(0, 0, second.angle);
      third.transform.localRotation = Quaternion.Euler(0, 0, third.angle);
    }

    public void Update()
    {
      SetRotation();

      var targetToRootDir = (target.position - first.transform.position).normalized;
      var minTwoLegsPos = first.transform.position + targetToRootDir * first.length;
      var maxTwoLegsPos = first.transform.position - targetToRootDir * first.length;

      // float angle = Mathf.Atan2(targetToRootDir.y, targetToRootDir.x) * Mathf.Rad2Deg;

      // first.transform.rotation = Quaternion.Euler(0, 0, angle);


      // only one solution if target too far
      // if (Vector2.Distance(minTwoLegsPos, target.position) >= second.length + third.length)
      // {
      //   Debug.Log($"exceed");
      //   // float angle = Mathf.Atan2(targetToRootDir.y, targetToRootDir.x) * Mathf.Rad2Deg;

      //   // first.transform.rotation = Quaternion.Euler(0, 0, angle);

      //   first.transform.right = targetToRootDir;
      //   second.transform.right = targetToRootDir;
      //   third.transform.right = targetToRootDir;
      // }
      // else
      // {

      // }






      second.transform.position = first.tailPosition;
      third.transform.position = second.tailPosition;
    }

    public void LateUpdate()
    {
      SolveTwoBoneIK();
    }
    
    void SolveTwoBoneIK()
    {
        // 计算目标方向向量
        Vector2 targetPosition = target.position;
        Vector2 directionToTarget = targetPosition - (Vector2)second.transform.position;
        
        // 计算到目标的距离
        float distanceToTarget = directionToTarget.magnitude;
        
        // 计算两段骨骼的总长度
        float totalArmLength = second.length + third.length;
        
        // 如果目标超出可到达范围，则按比例缩小距离
        if (distanceToTarget >= totalArmLength)
        {
            directionToTarget = directionToTarget.normalized * totalArmLength;
            targetPosition = (Vector2)second.transform.position + directionToTarget;
            distanceToTarget = totalArmLength;
        }
        
        // 计算根关节到目标的水平距离和垂直距离
        float horizontalDistance = Mathf.Sqrt(distanceToTarget * distanceToTarget - 
                                            directionToTarget.y * directionToTarget.y);
                                            

        horizontalDistance *= Mathf.Sign(directionToTarget.x);
        // 使用余弦定理计算第一段骨骼的角度（肘部角度）
        float upperAngle = Mathf.Acos(
            (second.length * second.length + distanceToTarget * distanceToTarget - 
             third.length * third.length) / 
            (2 * second.length * distanceToTarget));
        
        // 计算根关节的旋转角度
        Debug.Log($"upper angle: {upperAngle * Mathf.Rad2Deg}");
        float rootAngle = Mathf.Atan2(directionToTarget.y, horizontalDistance) + upperAngle;
        
        // 应用根关节旋转
        second.transform.localRotation = Quaternion.Euler(0, 0, rootAngle * Mathf.Rad2Deg);

        third.transform.position = second.tailPosition;
        
        // 计算第二段骨骼的角度（末端关节指向目标）
        Vector2 middleToEnd = third.tailPosition - (Vector2)third.transform.position;
        Vector2 middleToTarget = targetPosition - (Vector2)third.transform.position;
        float endAngle = Vector3.SignedAngle(middleToEnd, middleToTarget, Vector3.forward);
        
        // 应用中间关节旋转
        third.transform.Rotate(0, 0, endAngle);
        Debug.Log($"end angle: {endAngle}");
    }
    
    void OnDrawGizmos()
    {
      DrawBone(first);
      DrawBone(second);
      DrawBone(third);

      Gizmos.color = Color.white;
      Gizmos.DrawSphere(target.position, 0.2f);

      var rootToTargetDir = (target.position - first.transform.position).normalized;
      var minTwoLegsPos = first.transform.position + rootToTargetDir * first.length;
      var maxTwoLegsPos = first.transform.position - rootToTargetDir * first.length;

      Gizmos.DrawSphere(minTwoLegsPos, 0.1f);
      Gizmos.DrawSphere(maxTwoLegsPos, 0.1f);

    }

    void DrawBone(Bone bone)
    {
      if (bone.transform != null)
      {
        Gizmos.color = bone.color;
        Gizmos.DrawSphere(bone.transform.position, 0.1f);

        Gizmos.DrawWireSphere(bone.transform.position, bone.length);
      }

      GizmosExt.DrawLine(bone.transform.position, bone.tailPosition, Color.white, 10);
    }

}

