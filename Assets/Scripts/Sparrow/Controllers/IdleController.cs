

using System.Collections;
using UnityEngine;

namespace Sparrow
{
  public class IdleController : ActionController
  {
    public float IdleBetweenMinDuration = 0.2f;
    public float IdleBetweenMaxDuration = 2f;

    [SerializeField] private float poopMinDuration = 3f;
    [SerializeField] private float poopMaxDuration = 5f;

    [SerializeField] private float moveMinDuration = 2f;
    [SerializeField] private float moveChange = .5f;

    float idleElapsedTime = 0;
    private bool hasEat = false;

    void Update()
    {
      idleElapsedTime += Time.deltaTime;
    }

    public IEnumerator DoIdle()
    {
      yield return new WaitForSeconds(Random.Range(IdleBetweenMinDuration, IdleBetweenMaxDuration));

      if (idleElapsedTime > moveMinDuration && Random.Range(0f, 1f) < 0.5f && hasEat && brain.FlyPosition == FlyPosition.InGround)
      {
        TransitionTo(stateMachine.moveState);
      }
      else
      {
        stateMachine.idleState.PlayRandomActions();
      }
    }

    public IEnumerator DoPoop()
    {
      if (brain.IsFull && brain.FlyPosition == FlyPosition.InAir)
      {
        yield return new WaitForSeconds(Random.Range(poopMinDuration, poopMaxDuration));
        TransitionTo(stateMachine.poopState);
      }
    }

    public void Exit()
    {
      idleElapsedTime = 0;
      hasEat = false;
    }

    public void Eat(int amount)
    {
      hasEat = true;
      brain.Eat(amount);
    }

  }

}