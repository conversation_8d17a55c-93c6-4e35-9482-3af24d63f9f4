

using System.Collections;
using UnityEditor;
using UnityEngine;

namespace Sparrow
{
  public class MoveController : ActionController
  {
    [SerializeField] Vector2 moveRange;
    [SerializeField] float minMoveStep;
    [SerializeField] float maxMoveStep;
    [SerializeField] float moveSpeed;

    public void Move()
    {
      float leftMost = brain.Origin.x + moveRange.x;
      float rightMost = brain.Origin.x + moveRange.y;

      bool canMoveLeft = true;
      bool canMoveRight = true;

      if (transform.position.x - minMoveStep < leftMost)
      {
        canMoveLeft = false;
      }
      if (transform.position.x + minMoveStep > rightMost)
      {
        canMoveRight = false;
      }

      if (canMoveLeft && !canMoveRight)
      {
        var moveStep = Random.Range(minMoveStep, transform.position.x - leftMost);
        MoveBy(Vector2.left , moveStep);
      }
      else if (!canMoveLeft && canMoveRight)
      {
        var moveStep = Random.Range(minMoveStep, rightMost - transform.position.x);
        MoveBy(Vector2.right , moveStep);
      }
      else if (canMoveLeft && canMoveRight)
      {
        if (Random.Range(0, 2) == 0)
        {
          var moveStep = Random.Range(minMoveStep, leftMost - transform.position.x);
          MoveBy(Vector2.left , moveStep);
        }
        else
        {
          var moveStep = Random.Range(minMoveStep, rightMost - transform.position.x);
          MoveBy(Vector2.right , moveStep);
        }
      }
    }

    public void MoveBy(Vector2 dir, float distance)
    {
      brain.UpdateDirection(dir.x < 0 ? CharacterDirection.Left : CharacterDirection.Right);
      StartCoroutine(MoveByCoroutine(dir * Mathf.Clamp(distance, minMoveStep, maxMoveStep)));
    }

    IEnumerator MoveByCoroutine(Vector2 delta)
    {
      Vector2 target = (Vector2)transform.position + delta;

      while (!Mathf.Approximately(Vector2.Distance(transform.position, target), 0f))
      {
        transform.position = Vector2.MoveTowards(transform.position, target, moveSpeed * Time.deltaTime);
        brain.UpdateFlyPath();
        yield return null;
      }

      TransitionTo(stateMachine.idleState);
    }

    void OnDrawGizmos()
    {
      var origin = transform.position;
      Gizmos.color = Color.yellow;

      var startPosition = new Vector3(origin.x + moveRange.x, origin.y, origin.z);
      var endPosition = new Vector3(origin.x + moveRange.y, origin.y, origin.z);

      GizmosExt.DrawLine(startPosition, endPosition, Color.yellow);
    }

  }

}