

using System.Collections;
using System.IO;
using System.Linq;
using UnityEngine;
using UnityEngine.Splines;

namespace Sparrow
{
  public class FlyController : ActionController
  {
    [SerializeField] AnimationCurve curve;
    [SerializeField] float duration;
    [SerializeField] float disturbeDelay = .5f;
    private Coroutine flyCoroutine;
    private float elapsedTime = 0f;
    private bool isFlyingBack = false;

    public void DoFlyToAir()
    {
      flyCoroutine = StartCoroutine(FlyToAir());
    }

    public void DoFlyToGround()
    {
      flyCoroutine = StartCoroutine(FlyToGround());
    }

    public void DoFlyBackAir()
    {
      isFlyingBack = true;
      StartCoroutine(FlyBackAirCoroutine());
    }

    public IEnumerator FlyBackAirCoroutine()
    {
      yield return new WaitForSeconds(disturbeDelay);
      StopCoroutine(flyCoroutine);
      yield return StartCoroutine(FlyToAir());
    }

    public IEnumerator FlyToAir()
    {
      while (elapsedTime <= duration)
      {
        var t = curve.Evaluate(elapsedTime / duration);
        brain.transform.position = brain.FlyPath.EvaluatePosition(t);
        var tangent = brain.FlyPath.EvaluateTangent(t);
        if (tangent.x < 0)
        {
          brain.UpdateDirection(CharacterDirection.Left);
        }
        else
        {
          brain.UpdateDirection(CharacterDirection.Right);
        }


        yield return null;
        elapsedTime += Time.deltaTime;
      }

      elapsedTime = duration;
      brain.UpdateFlyPosition(FlyPosition.InAir);
      TransitionTo(stateMachine.idleState);
    }

    public IEnumerator FlyToGround()
    {
      while (elapsedTime >= 0f)
      {
        var t = curve.Evaluate(elapsedTime / duration);
        brain.transform.position = brain.FlyPath.EvaluatePosition(t);
        var tangent = brain.FlyPath.EvaluateTangent(t);
        if (tangent.x < 0)
        {
          brain.UpdateDirection(CharacterDirection.Right);
        }
        else
        {
          brain.UpdateDirection(CharacterDirection.Left);
        }


        yield return null;
        elapsedTime -= Time.deltaTime;
      }

      if (!isFlyingBack)
      {
        elapsedTime = 0f;
        brain.UpdateFlyPosition(FlyPosition.InGround);
        TransitionTo(stateMachine.idleState);
      }
    }

    public void Exit()
    {
      isFlyingBack = false;
    }

  }
  
 
}