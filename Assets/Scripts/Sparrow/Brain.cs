
using UnityEngine.Splines;
using UnityEngine;

namespace Sparrow
{
  public class Brain : CharacterBrain<Brain, StateMachine, State>, IDisturbable
  {
    public SplineContainer FlyPath;
    public Vector2 Origin { private set; get; }

    [HideInInspector] public FlyPosition FlyPosition { private set; get; } = FlyPosition.InGround;
    [SerializeField] int MaxHunger = 4;
    [SerializeField] int ExceedHunger = 8;

    [SerializeField] GameObject poopPrefab;
    [SerializeField] Transform poopSpawnPoint;

    private int hunger = 0;

    public bool IsFull { get => hunger >= MaxHunger; }
    public bool IsExceed { get => hunger >= ExceedHunger; }

    void Start()
    {
      StateMachine = new StateMachine(this);
      StateMachine.Initialize(StateMachine.idleState);

      transform.position = FlyPath.EvaluatePosition(0f);
      Origin = transform.position;
    }

    public void UpdateFlyPosition(FlyPosition flyPosition)
    {
      this.FlyPosition = flyPosition;
    }

    public void UpdateFlyPath()
    {
      var knot = FlyPath.Spline[0];
      knot.Position = transform.localPosition;
      FlyPath.Spline[0] = knot;
    }

    public void DisturbBy(IDisturber disturber, int level)
    {
      if ((StateMachine.CurrentState == StateMachine.idleState || StateMachine.CurrentState == StateMachine.moveState) && FlyPosition == FlyPosition.InGround)
      {
        StateMachine.TransitionTo(StateMachine.flyToAirState);
      }
      else if (StateMachine.CurrentState == StateMachine.flyToGroundState)
      {
        StateMachine.flyToGroundState.FlyBack();
      }
    }

    public void LoseDisturb(int level)
    {
      if (StateMachine.CurrentState == StateMachine.idleState && FlyPosition == FlyPosition.InAir)
      {
        StateMachine.TransitionTo(StateMachine.flyToGroundState);
      }
    }

    public void Eat(int amount)
    {
      hunger += amount;
    }

    void Poop()
    {
      hunger = 0;
      var poopObject = Instantiate(poopPrefab, poopSpawnPoint.position, Quaternion.identity);
    }
  }

  public enum FlyPosition
  {
    InAir,
    InGround,
  }
  
}