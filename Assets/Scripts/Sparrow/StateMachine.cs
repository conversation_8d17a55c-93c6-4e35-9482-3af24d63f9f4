
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Sparrow
{

  public class StateMachine : CharacterStateMachine<Brain, StateMachine, State>
  {

    public IdleState idleState;
    public FlyToGroundState flyToGroundState;
    public FlyToAirState flyToAirState;
    public PoopState poopState;
    public MoveState moveState;

    public StateMachine(Brain brain) : base(brain)
    {
      idleState = new IdleState(this);
      flyToGroundState = new FlyToGroundState(this);
      flyToAirState = new FlyToAirState(this);
      poopState = new PoopState(this);
      moveState = new MoveState(this);
    }

  }

}
