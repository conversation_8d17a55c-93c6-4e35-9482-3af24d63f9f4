using System.Collections;
using System.Collections.Generic;
using Sinner;
using UnityEngine;

public class PoopController : MonoBeh<PERSON>our
{
    [SerializeField] LayerMask layerMask;
    void OnCollisionEnter2D(Collision2D other)
    {
        // collider gameobject is character && has a edgeCollider detect collision
        if ((layerMask & (1 << other.gameObject.layer)) != 0 && other.collider is EdgeCollider2D)
        {
            ContactPoint2D[] contacts = new ContactPoint2D[1];
            var count = other.GetContacts(contacts);
            if (count > 0)
            {
                other.gameObject.TryGetComponent<Brain>(out var brain);
                if (brain != null)
                {
                    brain.AddPoop(contacts[0].point, contacts[0].normal);
                }
            }
        }
        Destroy(gameObject);
    }
}

