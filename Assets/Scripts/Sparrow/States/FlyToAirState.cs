
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Sparrow
{
  public class FlyToAirState : State
  {
    private readonly int flyHash = Animator.StringToHash("Fly");

    private FlyController flyController;
    public FlyToAirState(StateMachine stateMachine) : base(stateMachine)
    {
      flyController = brain.GetComponent<FlyController>();
    }

    public override HashSet<BaseActionController<Brain, StateMachine, State>> InitializeControllers()
    {
      return new HashSet<BaseActionController<Brain, StateMachine, State>>
          {
            flyController,
          };
    }

    public override void Enter(State prevState)
    {
      base.Enter(prevState);
      animator.CrossFade(flyHash, 0.1f);
      flyController.DoFlyToAir();
    }

    public override void Exit()
    {
      base.Exit();
      flyController.StopAllCoroutines();
      flyController.Exit();
    }

  }

}