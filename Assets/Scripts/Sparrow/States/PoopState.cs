
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Sparrow
{
  public class PoopState : State
  {
    private readonly int poopHash = Animator.StringToHash("Poop");

    public PoopState(StateMachine stateMachine) : base(stateMachine)
    {
    }

    public override HashSet<BaseActionController<Brain, StateMachine, State>> InitializeControllers()
    {
      return new HashSet<BaseActionController<Brain, StateMachine, State>>
          {
            
          };
    }

    public override void Enter(State prevState)
    {
      base.Enter(prevState);
      animator.CrossFade(poopHash, 0.1f);
    }

    public override void OnAnimFinish(int hash)
    {
      if (hash == poopHash)
      {
        TransitionTo(stateMachine.idleState);
      }
    }
  }

}