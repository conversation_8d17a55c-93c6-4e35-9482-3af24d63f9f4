
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Sparrow
{
  public class MoveState : State
  {
    private readonly int moveHash = Animator.StringToHash("Move");
    private MoveController moveController;

    public MoveState(StateMachine stateMachine) : base(stateMachine)
    {
      moveController = brain.GetComponent<MoveController>();
    }

    public override HashSet<BaseActionController<Brain, StateMachine, State>> InitializeControllers()
    {
      return new HashSet<BaseActionController<Brain, StateMachine, State>>
          {
            moveController
          };
    }

    public override void Enter(State prevState)
    {
      base.Enter(prevState);
      animator.CrossFade(moveHash, 0.1f);
      moveController.Move();
    }

    public override void Exit()
    {
      base.Exit();
      moveController.StopAllCoroutines();
    }

  }

}