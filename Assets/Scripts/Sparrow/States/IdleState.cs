
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Sparrow
{
  public class IdleState : State
  {
    private readonly int idleHash = Animator.StringToHash("Idle");
    private readonly int eat1Hash = Animator.StringToHash("Eat1");
    private readonly int eat2Hash = Animator.StringToHash("Eat2");
    private readonly int eat3Hash = Animator.StringToHash("Eat3");
    private readonly int sing1Hash = Animator.StringToHash("Sing1");
    private readonly int sing2Hash = Animator.StringToHash("Sing2");
    private readonly int shake1Hash = Animator.StringToHash("Shake1");
    private readonly int shake2Hash = Animator.StringToHash("Shake2");
    private int lastIndex = -1;

    private int[] groundIdleActions;
    private int[] airIdleActions;
    private IdleController idleController;
    public IdleState(StateMachine stateMachine) : base(stateMachine)
    {
      groundIdleActions = new int[]
      {
        sing1Hash,
        eat1Hash,
        eat2Hash,
        eat3Hash,
        shake1Hash,
      };

      airIdleActions = new int[]
      {
        sing1Hash,
        sing2Hash,
        shake1Hash,
        shake2Hash,
      };

      idleController = brain.GetComponent<IdleController>();
    }

    public override HashSet<BaseActionController<Brain, StateMachine, State>> InitializeControllers()
    {
      return new HashSet<BaseActionController<Brain, StateMachine, State>>
          {
            idleController,
          };
    }

    public override void Enter(State prevState)
    {
      base.Enter(prevState);

      if (prevState is FlyToGroundState || prevState is FlyToAirState)
      {
        animator.Play(idleHash);
      }
      else
      {
        animator.CrossFade(idleHash, 0.1f);
      }

      idleController.StartCoroutine(idleController.DoIdle());
      idleController.StartCoroutine(idleController.DoPoop());
    }

    public override void OnAnimFinish(int hash)
    {
      if (groundIdleActions.Contains(hash) || airIdleActions.Contains(hash))
      {
        if (hash == eat1Hash)
        {
          idleController.Eat(1);
        }
        else if (hash == eat2Hash)
        {
          idleController.Eat(2);
        }
        else if (hash == eat3Hash)
        {
          idleController.Eat(3);
        }

        animator.CrossFade(idleHash, 0.1f);
        idleController.StartCoroutine(idleController.DoIdle());
      }
    }

    public override void Exit()
    {
      base.Exit();

      idleController.StopAllCoroutines();
      idleController.Exit();
    }

    public void PlayRandomActions()
    {
      if (brain.FlyPosition == FlyPosition.InAir)
      {
        PlayRandomActions(airIdleActions);
      }
      else if (brain.FlyPosition == FlyPosition.InGround)
      {
        if (brain.IsExceed)
        {
          TransitionTo(stateMachine.poopState);
        }
        else
        {
          PlayRandomActions(groundIdleActions);
        }
      }
    }
    private void PlayRandomActions(int[] actions)
    {
      int index;
      do
      {
        index = Random.Range(0, actions.Length);
      } while (index == lastIndex);
      lastIndex = index;

      animator.CrossFade(actions[index], 0.1f);
    }
  }
}