
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Sparrow;
using UnityEngine;

[RequireComponent(typeof(Collider2D))]
public class GameAttackArea : MonoBehaviour
{
  [SerializeField] GameObject attackObject;

  void OnTriggerEnter2D(Collider2D other)
  {
    // other.TryGetComponent<IDamagable>(out var damagable);
    // attackObject.TryGetComponent<IDamager>(out var damager);

    // if (damager != null && damagable != null)
    // {
    //   // damager.DoDamage();
    //   var dir = (other.transform.position - attackObject.transform.position).normalized;
    //   damagable.TakeDamage(damager.DamageType, dir, damager.DamageForce);
    // }

  }
}

