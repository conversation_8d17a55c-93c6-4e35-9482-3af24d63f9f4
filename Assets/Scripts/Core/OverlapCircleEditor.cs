using UnityEditor;
using UnityEditor.IMGUI.Controls;
using UnityEngine;

[CustomEditor(typeof(OverlapCircle))]
public class OverlapCircleEditor : Editor
{
    private OverlapCircle circle;
    private SerializedProperty editModeProperty;
    private SphereBoundsHandle m_SphereBoundsHandle = new();

    private void OnEnable()
    {
        circle = (OverlapCircle)target;
        editModeProperty = serializedObject.FindProperty("editMode");
    }

    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();

        // 绘制编辑按钮
        EditorGUI.BeginChangeCheck();
        bool newEditMode = GUILayout.Toggle(editModeProperty.boolValue, 
                                           editModeProperty.boolValue ? "Stop Editing" : "Edit Collider", 
                                           "Button");
        if (EditorGUI.EndChangeCheck())
        {
            editModeProperty.boolValue = newEditMode;
            SceneView.RepaintAll(); // 刷新场景视图
        }
        serializedObject.ApplyModifiedProperties();
    }

    private void OnSceneGUI()
    {
        if (!circle.editMode)
            return;

        m_SphereBoundsHandle.axes = PrimitiveBoundsHandle.Axes.X | PrimitiveBoundsHandle.Axes.Y;
        m_SphereBoundsHandle.center = circle.transform.position + (Vector3)circle.centerOffset;
        m_SphereBoundsHandle.radius = circle.radius;

        EditorGUI.BeginChangeCheck();
        m_SphereBoundsHandle.DrawHandle();
        if (EditorGUI.EndChangeCheck())
        {
            // record the target object before setting new values so changes can be undone/redone
            Undo.RecordObject(circle, "Change Bounds");

            // copy the handle's updated data back to the target object
            circle.centerOffset = m_SphereBoundsHandle.center - circle.transform.position;
            circle.radius = m_SphereBoundsHandle.radius;
        }

        // 在场景视图中绘制可拖拽的手柄
        // EditorGUI.BeginChangeCheck();
        // Vector3 newPosition = Handles.PositionHandle(
        //     circle.transform.position + Vector3.right * circle.radius, 
        //     Quaternion.identity);

        // Vector3 newPosition = Handles.FreeMoveHandle(
        //     circle.transform.position + Vector3.right * circle.radius,
        //     0.2f,
        //     Vector3.zero,
        //     Handles.DotHandleCap);

        // if (EditorGUI.EndChangeCheck())
        // {
        //     Undo.RecordObject(circle, "Change Circle Radius");
        //     circle.radius = Vector3.Distance(newPosition, circle.transform.position);
        // }

        // // 绘制圆形
        // Handles.color = new Color(0, 1, 0, 0.8f);
        // Handles.DrawWireDisc(circle.transform.position, Vector3.forward, circle.radius);

        // 显示半径标签
        // Handles.Label(
        //     circle.transform.position + Vector3.right * circle.radius,
        //     "Radius: " + circle.radius.ToString("F2"));

        // circle.radius = Handles.RadiusHandle(
        //     circle.transform.rotation, 
        //     circle.transform.position, 
        //     circle.radius);

    }
}