


using System;
using System.ComponentModel;

public class ObservableData<T>
{
  private T _value;
  public T DefaultValue { private set; get; }
  public event Action<T> valueChanged;

  public T Value
  {
    get { return _value; }
    set
    {
      if (!_value.Equals(value))
      {
        _value = value;
        valueChanged?.Invoke(_value);
      }
    }
  }

  public ObservableData(T value)
  {
    _value = value;
    DefaultValue = value;
  }
}
