
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using UnityEngine.Events;


public class OverlapCircle : MonoBehaviour
{

  [System.Serializable]
  public class DisturbEvent : UnityEvent<GameObject, OverlapCircle> { }

  public float radius = 1f;
  public Vector2 centerOffset;
  [SerializeField] LayerMask layerMask;
  [SerializeField] float enterDelay = 0f;
  [SerializeField] float exitDelay = 0f;
  [SerializeField] DisturbEvent onEnter;
  [SerializeField] DisturbEvent onExit;

  [HideInInspector]
  public bool editMode = false;

  private Dictionary<Collider2D, (float enterElapsedTime, float exitElapsedTime, float entryTime)> overlapColliders = new Dictionary<Collider2D, (float, float, float)>();

  void OnDrawGizmos()
  {
    // Handles.color = new Color(0, 1, 0, 0.8f);
    // Handles.DrawWireDisc(transform.position, Vector3.forward, radius);
  }

  public GameObject GetOldestStayObject()
  {
    if (overlapColliders.Count == 0)
      return null;
      
    Collider2D oldestCollider = null;
    float oldestTime = float.MaxValue;
    
    foreach (var kvp in overlapColliders)
    {
      if (kvp.Value.entryTime < oldestTime)
      {
        oldestTime = kvp.Value.entryTime;
        oldestCollider = kvp.Key;
      }
    }
    
    return oldestCollider?.gameObject;
  }

  void Update()
  {
    Collider2D[] colliders = Physics2D.OverlapCircleAll(transform.position + (Vector3)centerOffset, radius, layerMask);
    HashSet<Collider2D> currentColliders = new HashSet<Collider2D>(colliders);
    
    // Process existing colliders
    // List<Collider2D> toRemove = new List<Collider2D>();
    // Create a copy of the keys to avoid modification during enumeration
    List<Collider2D> existingColliders = new List<Collider2D>(overlapColliders.Keys);
    
    foreach (var collider in existingColliders)
    {
      var (enterElapsedTime, exitElapsedTime, entryTime) = overlapColliders[collider];
      
      if (currentColliders.Contains(collider))
      {
        // Still overlapping
        if (enterElapsedTime <= enterDelay)
        {
          enterElapsedTime += Time.deltaTime;
          if (enterElapsedTime >= enterDelay)
          {
            // Debug.Log($"Enter {collider.name}");
            onEnter?.Invoke(collider.gameObject, this);
          }
        }
        exitElapsedTime = 0f;
        overlapColliders[collider] = (enterElapsedTime, exitElapsedTime, entryTime);
      }
      else
      {
        // No longer overlapping
        exitElapsedTime += Time.deltaTime;
        if (exitElapsedTime >= exitDelay)
        {
          // Debug.Log($"Exit {collider.name}");
          overlapColliders.Remove(collider);
          onExit?.Invoke(collider.gameObject, this);
          // toRemove.Add(collider);
        }
        else
        {
          overlapColliders[collider] = (enterElapsedTime, exitElapsedTime, entryTime);
        }
      }
    }
    
    // Add new colliders
    foreach (var collider in currentColliders)
    {
      if (!overlapColliders.ContainsKey(collider))
      {
        overlapColliders.Add(collider, (0f, 0f, Time.time));
      }
    }
  }
}
