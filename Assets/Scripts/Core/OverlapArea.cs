
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using UnityEngine.Events;


public class OverlapArea : MonoBehaviour
{
  [System.Serializable]
  public class DisturbEvent : UnityEvent<GameObject, OverlapArea> { }

  public Vector2 centerOffset;
  public Vector2 size;
  [SerializeField] LayerMask layerMask;
  [SerializeField] float enterDelay = 0f;
  [SerializeField] float exitDelay = 0f;
  [SerializeField] DisturbEvent onEnter;
  [SerializeField] DisturbEvent onExit;

  [HideInInspector]
  public bool editMode = false;

  private Dictionary<Collider2D, (float enterElapsedTime, float exitElapsedTime, float entryTime)> overlapColliders = new Dictionary<Collider2D, (float, float, float)>();

  private (Vector2 topLeft, Vector2 bottomRight) box
  {
    get
    {
      Vector2 position = (Vector2)transform.position + centerOffset;
      Vector2 halfSize = size / 2f;
      return (position - halfSize, position + halfSize);
    }
  }

  void OnDrawGizmos()
  {
    if (!enabled)
      return;
      
    Handles.color = Color.green;
    Handles.DrawWireCube(transform.position + (Vector3)centerOffset, size);
  }

  public GameObject GetOldestStayObject()
  {
    if (overlapColliders.Count == 0)
      return null;

    Collider2D oldestCollider = null;
    float oldestTime = float.MaxValue;

    foreach (var kvp in overlapColliders)
    {
      if (kvp.Value.entryTime < oldestTime)
      {
        oldestTime = kvp.Value.entryTime;
        oldestCollider = kvp.Key;
      }
    }

    return oldestCollider?.gameObject;
  }

  void Update()
  {
    Collider2D[] colliders = Physics2D.OverlapAreaAll(box.topLeft, box.bottomRight, layerMask);
    HashSet<Collider2D> currentColliders = new HashSet<Collider2D>(colliders);

    // Process existing colliders
    // List<Collider2D> toRemove = new List<Collider2D>();
    // Create a copy of the keys to avoid modification during enumeration
    List<Collider2D> existingColliders = new List<Collider2D>(overlapColliders.Keys);

    foreach (var collider in existingColliders)
    {
      var (enterElapsedTime, exitElapsedTime, entryTime) = overlapColliders[collider];

      if (currentColliders.Contains(collider))
      {
        // Still overlapping
        if (enterElapsedTime <= enterDelay)
        {
          enterElapsedTime += Time.deltaTime;
          if (enterElapsedTime >= enterDelay)
          {
            // Debug.Log($"Enter {collider.name}");
            onEnter?.Invoke(collider.gameObject, this);
          }
        }
        exitElapsedTime = 0f;
        overlapColliders[collider] = (enterElapsedTime, exitElapsedTime, entryTime);
      }
      else
      {
        // No longer overlapping
        exitElapsedTime += Time.deltaTime;
        if (exitElapsedTime >= exitDelay)
        {
          // Debug.Log($"Exit {collider.name}");
          overlapColliders.Remove(collider);
          onExit?.Invoke(collider.gameObject, this);
        }
        else
        {
          overlapColliders[collider] = (enterElapsedTime, exitElapsedTime, entryTime);
        }
      }
    }

    // Add new colliders
    foreach (var collider in currentColliders)
    {
      if (!overlapColliders.ContainsKey(collider))
      {
        overlapColliders.Add(collider, (0f, 0f, Time.time));
      }
    }
  }
}
