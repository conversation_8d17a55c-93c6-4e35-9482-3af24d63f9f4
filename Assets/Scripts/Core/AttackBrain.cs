

using System.Collections.Generic;
using UnityEngine;

public class AttackBrain<B, M, S> : CharacterBrain<B, M, S>, IDamager where B : AttackBrain<B, M, S> where M : CharacterStateMachine<B, M, S> where S : CharacterState<B, M, S>
{
  [Header("Damager")]
  [SerializeField] float damageForce; 
  [SerializeField] float damageRate; 

  public virtual DamagerType DamageType => DamagerType.None;
  public virtual float DamageForce => damageForce;

  // private Dictionary<IDamagable, float> lastDamageTimeMap = new Dictionary<IDamagable, float>();

  // !important! OnTriggerEnter2D must be only for attack detection
  void OnTriggerEnter2D(Collider2D other)
  {
    other.TryGetComponent<IDamagable>(out var damagable);

    if (damagable != null)
    {
      // if (lastDamageTimeMap.ContainsKey(damagable) && Time.time - lastDamageTimeMap[damagable] < damageRate)
      //   return;

      var dir = (other.transform.position - transform.position).normalized;
      if (damagable.CanTakeDamage())
      {
        damagable.TakeDamage(DamageType, dir, DamageForce);
      }
      // lastDamageTimeMap[damagable] = Time.time;
    }
  }

}
