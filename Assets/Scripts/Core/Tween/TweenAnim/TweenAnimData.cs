
using System;

// public abstract class TweenAnimData<T> where T : TweenAnimData<T>, new()
// {
//   public static TweenTimeTrack<T, P> CreateTimeTrack<P>(string fieldName, TweenTimeTrackChange<P> change)
//   {
//     var track = new TweenTimeTrack<T, P>(fieldName);
//     track.Change(change);
//     return track;
//   }
// }

// [AttributeUsage(AttributeTargets.Field, Inherited =true, AllowMultiple = false)]
// public abstract class TweenAnimAttribute : Attribute
// {
// }

[AttributeUsage(AttributeTargets.Field, Inherited =true, AllowMultiple = false)]
public sealed class TweenTimeTrackAttribute : Attribute
{

}


[AttributeUsage(AttributeTargets.Field, Inherited =true, AllowMultiple = false)]
public sealed class TweenSpeedTrackAttribute : Attribute
{

}
