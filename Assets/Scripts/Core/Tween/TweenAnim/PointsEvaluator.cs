
using System;
using UnityEngine;

public class PointsEvaluator<T>
{
  private T[] _values;
  private float[] _percents;
  private Func<T, T, float, T> _interpolateFunc = TweenHelper.Interpolate;

  public PointsEvaluator(T[] values, Func<T, T, float, T> interpolateFunc)
  {
    _values = values;
    _interpolateFunc = interpolateFunc;

    _percents = new float[values.Length - 1];
    float sum = TweenHelper.Sum(values);
    bool useCount = Mathf.Approximately(sum, 0f);
    if (useCount)
    {
      sum = values.Length - 1;
    }
    float currentDistance = 0f;
    for (int i = 0; i < values.Length - 1; ++i)
    {
      var diff = useCount ? 1f : TweenHelper.Distance(values[i], values[i + 1]);
      currentDistance += diff;
      _percents[i] = currentDistance / sum;
    }
  }

  public T Evaluate(float t)
  {
    if (_percents.Length == 0)
    {
      return _interpolateFunc(_values[0], _values[0], t);
    }

    for (int i = 0; i < _percents.Length; i++)
    {
      if (t <= _percents[i] || (t > 1.0f && i == _percents.Length - 1))
      {
        float lastPercent = 0f;
        if (i != 0)
        {
          lastPercent = _percents[i - 1];
        }

        return _interpolateFunc(_values[i], _values[i + 1], (t - lastPercent) / (_percents[i] - lastPercent));
      }
    }
    return default;
  }
}