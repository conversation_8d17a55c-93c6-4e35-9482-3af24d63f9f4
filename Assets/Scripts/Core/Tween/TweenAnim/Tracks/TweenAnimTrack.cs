using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

public class TweenAnimTrack<T> : TweenTrack<T> where T : new()
{
  public Dictionary<int, TweenAnimSubTrack<T>> _subTracks = new();

  public TweenAnimTrack(TweenAnim<T> tweenAnim, string fieldName) : base(tweenAnim, fieldName)
  {
  }
  public override bool IsCompleted => _subTracks.Values.All(t => t.IsCompleted);
  public override bool IsReachTarget => _subTracks.Values.All(t => t.IsReachTarget);
  public override bool IsValid => base.IsValid && _subTracks.Values.All(t => t.IsValid);

  public TweenAnimSubTrack<T> GetTrack(int layerIndex)
  {
    return _subTracks[layerIndex];
  }

  public override void Initialize()
  {
    if (!IsValid)
      return;

    // Initialize all subtracks first
    foreach (var subTrack in _subTracks.Values)
    {
      if (subTrack.IsValid)
      {
        subTrack.Initialize();
      }
    }

    // Merge values from all subtracks
    MergeTrackValues();
  }

  private void MergeTrackValues()
  {
    if (_subTracks.Count == 0)
      return;

    // Calculate total weight for normalization
    float totalWeight = 0f;
    foreach (var subTrack in _subTracks.Values)
    {
      totalWeight += subTrack.weight;
    }

    // Set normalized weights for each subtrack
    if (totalWeight > 0)
    {
      foreach (var subTrack in _subTracks.Values)
      {
        subTrack.normalizedWeight = subTrack.weight / totalWeight;
      }
    }
    else
    {
      // If total weight is 0, distribute evenly
      float equalWeight = 1f / _subTracks.Count;
      foreach (var subTrack in _subTracks.Values)
      {
        subTrack.normalizedWeight = equalWeight;
      }
    }

    // First, collect all unique features and count how many subtracks have each feature
    var featureCounts = new Dictionary<string, int>();
    var featureMap = new Dictionary<string, List<(AnimFeature.SingleFeature feature, float weight)>>();

    // First pass: count occurrences of each feature across all subtracks
    foreach (var subTrack in _subTracks.Values)
    {
      if (subTrack.updateValue == null)
        continue;

      var features = ((AnimFeature)subTrack.updateValue).features;
      if (features == null)
        continue;

      foreach (var feature in features)
      {
        // Create a unique key for each feature based on path, propertyName, and typeName
        string key = feature.Key;
        // // feature is out of range
        // if (subTrack.FeatureIsComplete(key))
        //   continue;

        // Count occurrences
        if (featureCounts.ContainsKey(key))
        {
          featureCounts[key]++;
        }
        else
        {
          featureCounts[key] = 1;
        }

        // Store feature with its weight for later processing
        if (!featureMap.ContainsKey(key))
        {
          featureMap[key] = new List<(AnimFeature.SingleFeature, float)>();
        }
        featureMap[key].Add((feature, subTrack.normalizedWeight));
      }
    }

    // Second pass: merge features based on count
    var allFeatures = new Dictionary<string, AnimFeature.SingleFeature>();

    foreach (var entry in featureMap)
    {
      string key = entry.Key;
      var featureList = entry.Value;
      bool multipleOccurrences = featureCounts[key] > 1;

      if (multipleOccurrences)
      {
        // If feature exists in multiple subtracks, apply weighted sum
        var mergedFeature = featureList[0].feature;
        mergedFeature.value *= featureList[0].weight;

        for (int i = 1; i < featureList.Count; i++)
        {
          mergedFeature.value += featureList[i].feature.value * featureList[i].weight;
        }

        allFeatures[key] = mergedFeature;
      }
      else
      {
        // If feature exists in only one subtrack, use original value without weight
        allFeatures[key] = featureList[0].feature;
      }
    }

    // Create final merged AnimFeature
    var mergedFeatures = new AnimFeature.SingleFeature[allFeatures.Count];
    int index = 0;
    foreach (var feature in allFeatures.Values)
    {
      mergedFeatures[index++] = feature;
    }

    updateValue = new AnimFeature() { features = mergedFeatures };
  }

  public override void ResetLoop()
  {
    foreach (var subTrack in _subTracks.Values)
    {
      subTrack.ResetLoop();
    }
  }

  public override void NextTick()
  {
    foreach (var subTrack in _subTracks.Values)
    {
      subTrack.NextTick();
    }

    CheckReachTarget();
  }

  public override void Update()
  {
    foreach (var subTrack in _subTracks.Values)
    {
      subTrack.Update();
    }

    // After updating all subtracks, merge their values
    MergeTrackValues();
  }

  public override void DoChange(TweenClip c)
  {
    if (c is not TweenAnimSubTrackClip change)
      return;

    if (_subTracks.ContainsKey(change.layerIndex))
    {
      if (change.animData == null)
      {
        _subTracks.Remove(change.layerIndex);
      }
      else
      {
        _subTracks[change.layerIndex].Change(c);
      }
    }
    else if (change.animData != null)
    {
      var subTrack = new TweenAnimSubTrack<T>(_tweenAnim, fieldName);
      _subTracks.Add(change.layerIndex, subTrack);
      subTrack.Change(c);
    }

    // Initialize();
  }

  public override void Rewind()
  {
    foreach (var subTrack in _subTracks.Values)
    {
      subTrack.Rewind();
    }
  }

}