using System;
using System.Linq;
using Unity.VisualScripting;
using UnityEngine;


public class TweenFreeAnimTrackClip : TweenClip
{
  public AnimData animData;
  public Func<float> GetTick;

  public int hash
  {
    get
    {
      int hash = animData?.GetHashCode() ?? 0;
      hash = HashCode.Combine(hash, GetTick?.GetHashCode() ?? 0);
      return hash;
    }
  }
}

public class TweenFreeAnimTrack<T> : TweenTrack<T> where T : new()
{
  public AnimData animData;
  public Func<float> _getTick;

  public TweenFreeAnimTrack(TweenAnim<T> tweenAnim, string fieldName) : base(tweenAnim, fieldName)
  {
  }

  public override float TotalDuration => -1;
  public override bool IsCompleted => false;
  public override bool IsValid => base.IsValid && animData != null && animData.bindings.Length > 0;

  public override void Initialize()
  {
    if (!IsValid)
      return;

    var features = new AnimFeature.SingleFeature[animData.bindings.Length];
    for (int i = 0; i < animData.bindings.Length; i++)
    {
      features[i] = new AnimFeature.SingleFeature()
      {
        path = animData.bindings[i].path,
        propertyName = animData.bindings[i].propertyName,
        typeName = animData.bindings[i].typeName,
        value = animData.bindings[i].curve.Evaluate(_getTick())
      };
    }
    updateValue = new AnimFeature() { features = features };
  }

  public override void Update()
  {
    var features = ((AnimFeature)updateValue).features;
    for (int i = 0; i < animData.bindings.Length; i++)
    {
      features[i].value = animData.bindings[i].curve.Evaluate(_getTick());
    }
  }

  public override void DoChange(TweenClip c)
  {
    if (c is not TweenFreeAnimTrackClip change)
      return;

    animData = change.animData;
    _getTick = change.GetTick;

    Initialize();
  }


}