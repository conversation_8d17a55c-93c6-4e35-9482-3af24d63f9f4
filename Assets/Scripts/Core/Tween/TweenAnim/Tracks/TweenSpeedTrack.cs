
using System;
using UnityEngine;

public class TweenSpeedTrackClip<T> : TweenClip
{
  public Func<T> GetStartValue = null;
  public Func<T> GetTargetValue;
  public Func<float> GetSpeed;
  public bool? completable = null;
  public Func<T, T, float, T> interpolateFunc = null;
  public Func<T, T, bool> compareFunc = null;

  public int hash
  {
    get
    {
      int hash = GetStartValue?.GetHashCode() ?? 0;
      hash = HashCode.Combine(hash, GetTargetValue?.GetHashCode() ?? 0);
      hash = HashCode.Combine(hash, GetSpeed?.GetHashCode() ?? 0);
      hash = HashCode.Combine(hash, completable?.GetHashCode() ?? 0);
      hash = HashCode.Combine(hash, interpolateFunc?.GetHashCode() ?? 0);
      hash = HashCode.Combine(hash, compareFunc?.GetHashCode() ?? 0);
      return hash;
    }
  }
}


public class TweenSpeedTrack<T, P> : TweenTrack<T> where T : new()
{
  public Func<P> GetStartValue;
  public Func<P> GetTargetValue;
  public Func<float> GetSpeed;
  private Func<P, P, float, P> _interpolateFunc = TweenHelper.InterpolateSpeed;
  private Func<P, P, bool> _compareFunc = TweenHelper.ValueEqual;
  private bool _completable = false;

  public TweenSpeedTrack(TweenAnim<T> tweenAnim, string fieldName) : base(tweenAnim, fieldName)
  {
  }

  public override float TotalDuration => -1f;
  public override bool IsCompleted => _completable && IsReachTarget;
  public override bool IsReachTarget => _compareFunc((P)updateValue, _tweenAnim.Reverse ? GetStartValue() : GetTargetValue());
  public override bool IsValid => base.IsValid && GetStartValue != null && GetTargetValue != null && GetSpeed != null;

  public override void Initialize()
  {
    if (IsValid)
    updateValue = GetStartValue();
  }

  public override void Update()
  {
    if (delayedTime < delay)
    {
      delayedTime += Time.deltaTime;
      return;
    }

    updateValue = _interpolateFunc((P)updateValue, _tweenAnim.Reverse && _completable ? GetStartValue() : GetTargetValue(), GetSpeed());
    CheckReachTarget();
  }

  public override void DoChange(TweenClip c)
  {
    if (c is not TweenSpeedTrackClip<P> change)
      return;

    if (change.GetStartValue == null)
    {
      var startValue = GetUpdateValue<P>();
      change.GetStartValue = () => startValue;
    }

    GetStartValue = change.GetStartValue;
    GetTargetValue = change.GetTargetValue;
    GetSpeed = change.GetSpeed;
    _completable = change.completable ?? false;
    _interpolateFunc = change.interpolateFunc ?? TweenHelper.InterpolateSpeed;
    _compareFunc = change.compareFunc ?? TweenHelper.ValueEqual;

    Initialize();
  }
}
