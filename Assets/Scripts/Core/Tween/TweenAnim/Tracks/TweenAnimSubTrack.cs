using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;


public class TweenAnimSubTrackClip : TweenClip
{
  public AnimData animData;
  public int layerIndex = 0;
  public float weight = 0f;
  public float? crossDuration = null;
  public EaseType? crossEaseType = null;
  public Func<float, float> crossEaseFunc = null;
  public AnimationCurve crossCurve = null;
  public bool? reverse = null;
  public float? speed = null;
  public int? loopCount = null;
  public bool? pingpong = null;

  public int hash
  {
    get
    {
      int hash = animData?.GetHashCode() ?? 0;
      hash = HashCode.Combine(hash, layerIndex);
      hash = HashCode.Combine(hash, weight.GetHashCode());
      hash = HashCode.Combine(hash, crossDuration?.GetHashCode() ?? 0);
      hash = HashCode.Combine(hash, crossEaseType?.GetHashCode() ?? 0);
      hash = HashCode.Combine(hash, crossCurve?.GetHashCode() ?? 0);
      hash = HashCode.Combine(hash, reverse?.GetHashCode() ?? 0);
      hash = HashCode.Combine(hash, speed?.GetHashCode() ?? 0);
      hash = HashCode.Combine(hash, loopCount?.GetHashCode() ?? 0);
      hash = HashCode.Combine(hash, pingpong?.GetHashCode() ?? 0);
      return hash;
    }
  }
}

public class TweenAnimSubTrack<T> : TweenTrack<T> where T : new()
{
  public AnimData animData;
  public int layerIndex = 0;
  public float weight = 0f;
  public float crossDuration;
  public EaseType crossEaseType = EaseType.Linear;
  public Func<float, float> crossEaseFunc = null;
  public AnimationCurve crossCurve = null;

  public float speed = 1f;
  public bool reverse = false;
  public int loopCount = 1;
  public bool pingpong = false;
  private int _loopTimes = 0;
  private bool _reverse = false;
  private float _startTime;
  private float _crossStartTime;
  private TweenAnimSubTrack<T> _lastTrack = null;

  public TweenAnimSubTrack(TweenAnim<T> tweenAnim, string fieldName) : base(tweenAnim, fieldName)
  {
  }
  private TweenAnimSubTrack<T> Copy() => this.MemberwiseClone() as TweenAnimSubTrack<T>;

  public float normalizedWeight;
  
  public float Duration => speed > 0f && animData != null ? animData.duration / speed : 0f;
  // public override float TotalDuration => loopCount >= 0 ? Duration * loopCount + _timeOffset : float.PositiveInfinity;
  public override bool IsReachTarget => loopCount > 0 ? _loopTimes >= loopCount : false;
  public override bool IsValid => base.IsValid && animData != null && animData.bindings.Length > 0 && Duration >= 0f;
  private bool IsReverse => (_tweenAnim.Reverse && !_reverse) || (!_tweenAnim.Reverse && _reverse);
  private float ElapsedTime => _tweenAnim.ElapsedTime - _startTime;
  private float CrossElapsedTime => _tweenAnim.ElapsedTime - _crossStartTime;

  public float normalizedTime => Mathf.Clamp01(ElapsedTime / Duration);
  public int animHash => animData.GetHashCode();

  // float GetCurveDuration(AnimationCurve curve)
  // {
  //   return curve[curve.length - 1].time;
  // }

  public override void Initialize()
  {
    if (!IsValid)
      return;

    _startTime = _tweenAnim.ElapsedTime;
    _crossStartTime = _tweenAnim.ElapsedTime;

    var features = new AnimFeature.SingleFeature[animData.bindings.Length];
    for (int i = 0; i < features.Length; i++)
    {
      features[i] = new AnimFeature.SingleFeature()
      {
        path = animData.bindings[i].path,
        propertyName = animData.bindings[i].propertyName,
        typeName = animData.bindings[i].typeName,
        value = animData.bindings[i].curve.Evaluate(0)
      };
    }

    updateValue = new AnimFeature() { features = features };
  }

  public override void ResetLoop()
  {
    _loopTimes = 0;
    _reverse = reverse;
        _startTime = _tweenAnim.ElapsedTime;
  }

  public bool FeatureIsComplete(string key)
  {
    // if (_featureCompleteMap.ContainsKey(key))
    //   return _featureCompleteMap[key];
    return false;
  }

  float GetTick()
  {
    var t = ElapsedTime * speed;
    if (IsReverse)
      return Mathf.Clamp(Duration * speed - t, 0, Duration * speed);
    return Mathf.Clamp(t, 0, Duration * speed);
  }

  public override void NextTick()
  {
    if ((_loopTimes < loopCount || loopCount < 0) && ElapsedTime >= Duration && CrossElapsedTime >= crossDuration)
    {
      ++_loopTimes;
      if (_loopTimes < loopCount || loopCount < 0)
      {
        _startTime = _tweenAnim.ElapsedTime;
      }

      CheckReachTarget();

      if (pingpong)
      {
        _reverse = !_reverse;
      }
    }
  }

  private float GetWeight()
  {
    if (_lastTrack == null || !_lastTrack.IsValid || Mathf.Approximately(crossDuration, 0f))
      return 1f;
    
    var originalWeight = Mathf.Clamp01(CrossElapsedTime / crossDuration);
    return TweenHelper.Ease(crossCurve, crossEaseFunc, crossEaseType, originalWeight);
  }

  public override void Update()
  {
    if (IsCompleted)
      return;

    var features = (AnimFeature.SingleFeature[])((AnimFeature)updateValue).features.Clone();

    var weight = GetWeight();
    var t = GetTick();

    for (int i = 0; i < animData.bindings.Length; ++i)
    {
      var value = GetValue(animData.bindings[i], t);
      if (weight < 1f)
      {
        var curveType = animData.bindings[i].curveType;
        var lastValue = _lastTrack.GetValue(animData.bindings[i].path, animData.bindings[i].typeName, animData.bindings[i].propertyName);
        if (curveType == CurveType.Euler)
        {
          var toValue = GetValue(animData.bindings[i], 0);
          if (toValue.HasValue && lastValue.HasValue)
          {
            lastValue = MathfExt.GetClosestEquivalentAngle(toValue.Value, lastValue.Value);
          }
        }

        if (value.HasValue &&  lastValue.HasValue)
        {
          features[i].value = value.Value * weight + lastValue.Value * (1f - weight);
        }
        else if (value.HasValue)
        {
          features[i].value = value.Value;
        }
        else if (lastValue.HasValue)
        {
          features[i].value = lastValue.Value;
        }
      }
      else
      {
        if (value.HasValue)
        {
          features[i].value = value.Value;
        }
      }

      // update feature complete map
      // _featureCompleteMap[features[i].Key] = t > GetCurveDuration(animData.bindings[i].curve);
    }

    updateValue = new AnimFeature() { features = features };
  }

  private float? GetValue(AnimData.Binding binding, float t)
  {
    if (!IsValid || binding  == null)
      return null;

    return binding.curve.Evaluate(t);
  }

  private float? GetValue(string path, string typeName, string propertyName)
  {
    var binding = animData.bindings.FirstOrDefault(b => b.path == path && b.typeName == typeName && b.propertyName == propertyName);
    return GetValue(binding, GetTick());
  }

  public override void DoChange(TweenClip c)
  {
    if (c is not TweenAnimSubTrackClip change)
      return;

    _lastTrack = Copy();

    animData = change.animData;
    layerIndex = change.layerIndex;
    weight= change.weight;
    reverse = change.reverse ?? false;
    speed = Mathf.Max(change.speed ?? 1f, 0f);
    loopCount = change.loopCount ?? 1;
    pingpong = change.pingpong ?? false;
    crossDuration = change.crossDuration ?? 0f;
    crossEaseType = change.crossEaseType ?? EaseType.Linear;
    crossEaseFunc = change.crossEaseFunc;
    crossCurve = change.crossCurve;

    _reverse = _tweenAnim.Reverse ? !reverse : reverse;
    _loopTimes = 0;

    Initialize();
  }

  public override void Rewind()
  {
    _startTime = _tweenAnim.ElapsedTime;
    _loopTimes = 0;
    _reverse = reverse;
  }

}
