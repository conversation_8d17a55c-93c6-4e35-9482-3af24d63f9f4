using System;
using UnityEngine;

public class TweenTimeTrackClip<T> : TweenClip
{
  public float? delay = null;
  public float? duration = null;
  public T[] values;
  public float? speed = null;
  public bool? reverse = null;
  public int? loopCount = null;
  public bool? pingpong = null;
  public EaseType? easeType = null;
  public Func<float, float> easeFunc = null;
  public AnimationCurve curve = null;
  public float? crossDuration = null;
  public EaseType? crossEaseType = null;
  public Func<float, float> crossEaseFunc = null;
  public AnimationCurve crossCurve = null;
  public Func<T, T, float, T> interpolateFunc = null;

  public int hash
  {
    get
    {
      int hash = delay?.GetHashCode() ?? 0;
      hash = HashCode.Combine(hash, duration?.GetHashCode() ?? 0);
      hash = HashCode.Combine(hash, values?.GetHashCode() ?? 0);
      hash = HashCode.Combine(hash, speed?.GetHashCode() ?? 0);
      hash = HashCode.Combine(hash, reverse?.GetHashCode() ?? 0);
      hash = HashCode.Combine(hash, loopCount?.GetHashCode() ?? 0);
      hash = HashCode.Combine(hash, pingpong?.GetHashCode() ?? 0);
      hash = HashCode.Combine(hash, easeType?.GetHashCode() ?? 0);
      hash = HashCode.Combine(hash, easeFunc?.GetHashCode() ?? 0);
      hash = HashCode.Combine(hash, curve?.GetHashCode() ?? 0);
      hash = HashCode.Combine(hash, crossDuration?.GetHashCode() ?? 0);
      hash = HashCode.Combine(hash, crossEaseType?.GetHashCode() ?? 0);
      hash = HashCode.Combine(hash, crossEaseFunc?.GetHashCode() ?? 0);
      hash = HashCode.Combine(hash, crossCurve?.GetHashCode() ?? 0);
      hash = HashCode.Combine(hash, interpolateFunc?.GetHashCode() ?? 0);
      return hash;
    }
  }
}


public class TweenTimeTrack<T, P> : TweenTrack<T> where T : new()
{
  public float duration = 0f;
  public P[] values;
  public AnimationCurve curve;
  public EaseType easeType = EaseType.Linear;
  public Func<float, float> easeFunc;
  public float speed = 1f;
  public bool reverse = false;
  public int loopCount = 1;
  public bool pingpong = false;
  public float crossDuration = 0f;
  public EaseType crossEaseType = EaseType.Linear;
  public Func<float, float> crossEaseFunc = null;
  public AnimationCurve crossCurve = null;
  private int _loopTimes = 0;
  private bool _reverse = false;
  private float _startTime;
  private float _crossStartTime;
  public Func<P, P, float, P> _interpolateFunc = TweenHelper.Interpolate;
  private PointsEvaluator<P> _ev;

  private TweenTimeTrack<T, P> _lastTrack = null;

  public TweenTimeTrack(TweenAnim<T> tweenAnim, string fieldName) : base(tweenAnim, fieldName)
  {
  }

  private TweenTimeTrack<T, P> Copy() => this.MemberwiseClone() as TweenTimeTrack<T, P>;

  public float Duration => speed > 0f ? duration / speed : 0f;

  // public override float TotalDuration => loopCount >= 0 ? Duration * loopCount + _timeOffset : float.PositiveInfinity;
  public override bool IsReachTarget => loopCount > 0 ? _loopTimes >= loopCount : false;
  public override bool IsValid => base.IsValid && Duration >= 0f && values != null && values.Length > 0;

  private bool IsReverse => (_tweenAnim.Reverse && !_reverse) || (!_tweenAnim.Reverse && _reverse);
  private float ElapsedTime => _tweenAnim.ElapsedTime - _startTime;
  private float CrossElapsedTime => _tweenAnim.ElapsedTime - _crossStartTime;

  public override void Initialize()
  {
    if (!IsValid)
      return;

    _startTime = _tweenAnim.ElapsedTime;
    _crossStartTime = _tweenAnim.ElapsedTime;
    _ev = new PointsEvaluator<P>(values, _interpolateFunc);
  }

  float GetTick()
  {
    var t = Mathf.Clamp01(Duration > 0 ? ElapsedTime / Duration : 0);

    if (IsReverse)
    {
      return TweenHelper.Ease(curve, easeFunc, easeType, 1 - t);
    }
    return TweenHelper.Ease(curve, easeFunc, easeType, t);
  }

  public override void NextTick()
  {
    if ((_loopTimes < loopCount || loopCount < 0) && ElapsedTime >= Duration)
    {
      _startTime = _tweenAnim.ElapsedTime;
      ++_loopTimes;
      if (pingpong)
      {
        _reverse = !_reverse;
      }

      CheckReachTarget();

    }
    // detect cross fade over
    if (_lastTrack != null && _lastTrack.IsValid && CrossElapsedTime > crossDuration)
    {
      _lastTrack = null;
    }
  }
  public override void ResetLoop()
  {
    _loopTimes = 0;
    _reverse = reverse;
    _startTime = _tweenAnim.ElapsedTime;
  }

  private float GetWeight()
  {
    if (_lastTrack == null || !_lastTrack.IsValid || Mathf.Approximately(crossDuration, 0f)) return 1f;
    var originalWeight = Mathf.Clamp01(CrossElapsedTime / crossDuration);
    return TweenHelper.Ease(crossCurve, crossEaseFunc, crossEaseType, originalWeight);
  }

  public override void Update()
  {
    var weight = GetWeight();

    var lastValue = _lastTrack?.GetValue();
    var value = GetValue();
    if (lastValue != null)
    {
      // Debug.Log($"{weight}, {lastValue}");
    }

    if (lastValue != null && value != null)
    {
      updateValue = TweenHelper.Add(value.Weighted(weight), lastValue.Weighted(1f - weight));
    }
    else if (lastValue != null)
    {
      updateValue = lastValue;
    }
    else if (value != null)
    {
      updateValue = value;
    }
  }

  private object GetValue()
  {
     if (!IsValid)
      return null;

    if (delayedTime < delay)
    {
      delayedTime += Time.deltaTime;
      if (delayedTime >= delay)
      {
        _startTime = _tweenAnim.ElapsedTime;
        _crossStartTime = _tweenAnim.ElapsedTime;
      }
      return null;
    }

    var t = GetTick();

    return _ev.Evaluate(t);
  }

  public override void DoChange(TweenClip c)
  {
    if (c is not TweenTimeTrackClip<P> change)
      return;
    _lastTrack = Copy();

    delay = change.delay ?? 0f;
    duration = change.duration ?? 0f;
    easeType = change.easeType ?? EaseType.Linear;
    speed = Mathf.Max(change.speed ?? 1f, 0f);
    reverse = change.reverse ?? false;
    loopCount = change.loopCount ?? 1;
    pingpong = change.pingpong ?? false;
    easeFunc = change.easeFunc ?? null;
    curve = change.curve ?? null;
    crossDuration = change.crossDuration ?? 0f;
    crossEaseType = change.crossEaseType ?? EaseType.Linear;
    crossEaseFunc = change.crossEaseFunc ?? null;
    crossCurve = change.crossCurve ?? null;

    _interpolateFunc = change.interpolateFunc ?? TweenHelper.Interpolate;

    _reverse = _tweenAnim.Reverse ? !reverse : reverse;
    _loopTimes = 0;

    // values
    values = change.values;

    Initialize();
  }

  public override void Rewind()
  {
    _startTime = _tweenAnim.ElapsedTime;
    _loopTimes = 0;
    _reverse = reverse;
  }

}
