
using System;

public readonly struct TweenFloatArray
{
  private readonly float[] _values;

  // 构造函数
  public TweenFloatArray(params float[] values)
  {
    _values = values;
  }

  // 支持标量乘法
  public static TweenFloatArray operator *(TweenFloatArray array, float factor)
  {
    float[] result = new float[array._values.Length];
    for (int i = 0; i < array._values.Length; i++)
    {
      result[i] = array._values[i] * factor;
    }
    return new TweenFloatArray(result);
  }

  // 转换为 float[]
  public float[] ToArray() => (float[])_values.Clone();

  // 隐式转换（可选）
  public static implicit operator float[](TweenFloatArray array) => array._values;
  public static implicit operator TweenFloatArray(float[] array) => new(array);

  // 示例属性（类似你的 FloatIdentity）
  public static TweenFloatArray Identity => new TweenFloatArray(new[] { 0f, 1f });
}

