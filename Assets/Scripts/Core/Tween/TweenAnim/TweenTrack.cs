
using System;
using UnityEngine;

public abstract class TweenTrack<T> where T : new ()
{
  public string fieldName;
  public float delay = 0f;
  public float delayedTime = 0f;
  public object updateValue = null;

  private bool _isPaused = false;

  protected TweenAnim<T> _tweenAnim;

  public virtual float TotalDuration { get; }
  public virtual bool IsCompleted => IsReachTarget;
  public virtual bool IsReachTarget { get; }
  public virtual bool IsValid => _tweenAnim != null;
  public virtual bool IsPaused => _isPaused;

  public event Action reachTarget;
  public event Action removed;
  public event Action clipChanged;

  public TweenClip currentClip { get; private set; } = null;

  public TweenTrack(TweenAnim<T> tweenAnim, string fieldName) 
  {
    this.fieldName = fieldName;
    _tweenAnim = tweenAnim;
  }

  // public void Initialize(TweenAnim<T> tweenAnim) { 
  //   _tweenAnim = tweenAnim;
  //   Initialize();
  // }

  public void Pause() {
    _isPaused = true;
  }
  public void Resume() {
    _isPaused = false;
  }
  public void Change(TweenClip clip) {
    if (clip == currentClip)
      return;
    currentClip = clip;
    _isPaused = false;
    DoChange(clip);
    clipChanged?.Invoke();
  }

  protected P GetUpdateValue<P>()
  {
    var field = typeof(T).GetField(fieldName);
    return (P)field.GetValue(_tweenAnim.Data.Value);
  }

  public virtual void Initialize() {}
  public virtual void Rewind() {}
  public virtual void DoChange(TweenClip change) {}
  public virtual void Update() { }
  public virtual void NextTick() {}
  public virtual void ResetLoop() {}

  protected void CheckReachTarget()
  {
    if (IsReachTarget)
    {
      reachTarget?.Invoke();
    }
  }


  public void Destroy()
  {
    _tweenAnim = null;
    removed?.Invoke();
    removed = null;
    reachTarget = null;
    clipChanged = null;
  }
}


public interface TweenClip
{
  public int hash { get; }
}