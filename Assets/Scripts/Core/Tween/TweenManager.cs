

using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Splines;

public class TweenManager : MonoBehaviour
{
  private static TweenManager _instance;

  public static TweenManager Instance
  {
    get
    {
      if (_instance == null)
      {
        var gameObject = new GameObject("TweenManager");
        _instance = gameObject.AddComponent<TweenManager>();
      }
      return _instance;
    }
  }

  private Dictionary<string, ITween> _tweens = new Dictionary<string, ITween>();

  void Update()
  {
    foreach (var tweenPair in _tweens.ToList())
    {
      var tween = tweenPair.Value;
      tween.Update();
      if (tween.IsComplete && tween.Completable && _tweens[tween.Identifier] == tween)
      {
        _tweens.Remove(tween.Identifier);

        if (!tween.wasKilled && tween.GetNextTween != null)
        {
          var nextTween = tween.GetNextTween();
          nextTween.Start();
        }
      }
      else if (tween.wasKilled && _tweens[tween.Identifier] == tween)
      {
        _tweens.Remove(tween.Identifier);
      }
    }
  }

  public void AddTween(ITween tween)
  {
    if (_tweens.ContainsKey(tween.Identifier) && _tweens[tween.Identifier] != tween)
    {
      _tweens[tween.Identifier].Kill();
      RemoveTween(tween.Identifier);
    }

    _tweens.TryAdd(tween.Identifier, tween);
  }
  public void RemoveTween(ITween tween)
  {
    _tweens.Remove(tween.Identifier);
  }
  public void RemoveTween(string identifier)
  {
    _tweens.Remove(identifier);
  }
}