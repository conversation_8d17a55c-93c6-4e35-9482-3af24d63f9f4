

using System;
using UnityEngine;
using Tweening;

public class TweenTime<T> : TweenTimeGeneral<T> where T : TweenTime<T>
{
  public float Delay { private set; get; }

  private float _delayedTime = 0f;
  private EaseType _easeType = EaseType.Linear;
  private Func<float, float> _easeFunc;
  private AnimationCurve _curve;

  public TweenTime(object target, string identifier, float duration) : base(target, identifier, true)
  {
    Duration = duration;
  }

  protected float GetTick()
  {
    var t = Mathf.Clamp01(_elapsedTime / Duration);
    if (_reverse)
    {
      return Ease(_curve, _easeType, 1 - t, _easeFunc);
    }
    return Ease(_curve, _easeType, t, _easeFunc);
  }

  protected override bool CanUpdate()
  {
    if (!base.CanUpdate())
      return false;

    if (_delayedTime < Delay)
    {
      _delayedTime += Time.deltaTime;
      return false;
    }
    return true;
  }

  public T SetDelay(float delay)
  {
    Delay = delay;
    return (T)this;
  }

  public T SetEase(EaseType easeType)
  {
    _easeType = easeType;
    return (T)this;
  }

  public T SetEaseFunc(Func<float, float> func)
  {
    _easeFunc = func;
    return (T)this;
  }

  public T SetAnimationCurve(AnimationCurve curve)
  {
    _curve = curve;
    return (T)this;
  }
}