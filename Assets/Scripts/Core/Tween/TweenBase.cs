

using System;
using UnityEngine;
using System.Collections.Generic;

public class TweenBase<T> : ITween where T : TweenBase<T>
{
  public string Identifier { private set; get; }

  public object Target { private set; get; }

  protected Action _onUpdate;
  protected Action _onComplete;

  private bool _completable = true;
  private bool _isComplete = false;
  private bool _isPaused = false;
  private bool _wasKilled = false;
  private Func<ITween> _getNextTween;

  public TweenBase(object target, string identifier, bool completable)
  {
    Target = target;
    Identifier = identifier;
    _completable = completable;
  }

  public bool wasKilled => _wasKilled;
  public bool IsComplete
  {
    get => _isComplete;
    protected set => _isComplete = value;
  }
  public bool Completable => _completable;
  public bool IsPaused => _isPaused;

  public bool IsTargetDestroyed
  {
    get
    {
      if (Target is GameObject ob && ob == null)
        return true;
      return false;
    }
  }

  public Func<ITween> GetNextTween { get => _getNextTween; }

  protected void OnComplete()
  {
    IsComplete = true;
    _onUpdate = null;

    _onComplete?.Invoke();
    _onComplete = null;

    // if (_getNextTween != null)
    // {
    //   var nextTween = _getNextTween();
    //   nextTween.Start();
    // }
  }

  public virtual void Kill()
  {
    _wasKilled = true;
    IsComplete = true;
    _onUpdate = null;
    _onComplete = null;
  }

  public ITween Then(Func<ITween> getNextTween)
  {
    if (_getNextTween == null)
    {
      _getNextTween = getNextTween;
    }
    else
    {
      var originalGetNextTween = _getNextTween;
      _getNextTween = () => {
        var nextTween = originalGetNextTween();
        nextTween.Then(getNextTween);
        return nextTween;
      };
    }

    return this;
  }

  public void Start()
  {
    TweenManager.Instance.AddTween(this);
  }

  public void Pause()
  {
      _isPaused = true;
  }

  public void Resume()
  {
      _isPaused = false;
  }
  protected virtual bool CanUpdate()
  {
    if (IsTargetDestroyed)
    {
      OnComplete();
      return false;
    }

    if (IsComplete || _isPaused)
      return false;

    return true;
  }


  public virtual void Update()
  {
  }

  public T SetOnUpdate(Action onUpdate)
  {
    _onUpdate = onUpdate;
    return (T)this;
  }

  public T SetOnComplete(Action onComplete)
  {
    _onComplete = onComplete;
    return (T)this;
  }
}