

using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using UnityEngine;
using UnityEngine.Splines;

public static class Tween
{
  // public static T[] Identity<T>()
  // {
  //   if (typeof(T) == typeof(float))
  //   {
  //     return new T[] { (T)(object)0f, (T)(object)1f };
  //   }
  //   throw new NotImplementedException();
  // }

  public static TweenFloatArray FloatIdentity => TweenFloatArray.Identity;

  public static TweenDiscrete<Vector2> TweenPosition(this Transform transform, Vector2[] positions, float duration)
  {
    string identifier = $"{transform.GetInstanceID()}.Position";
    var tween = new TweenDiscrete<Vector2>(transform.gameObject, identifier, positions, duration, val => {
      transform.position = val;
    });
    return tween;
  }
  public static TweenDiscrete<Quaternion> TweenRotation(this Transform transform, Quaternion[] rotations, float duration)
  {
    string identifier = $"{transform.GetInstanceID()}.Rotation";
    var tween = new TweenDiscrete<Quaternion>(transform.gameObject, identifier, rotations, duration, val => {
      transform.rotation = val;
    });
    return tween;
  }
  // public static TweenDiscreteCompose TweenCompose(this GameObject gameObject, TweenSingle[] tweenSingles, Action<object[]> SetValues)
  // {
  //   string identifier = $"{gameObject.GetInstanceID()}.Compose";
  //   var tween = new TweenDiscreteCompose(gameObject, identifier, tweenSingles, SetValues);
  //   return tween;
  // }
  public static TweenSpline TweenSpline(this Transform transform, SplineContainer splineContainer, float duration, bool applyRotation = false)
  {
    string identifier = $"{transform.GetInstanceID()}.Spline";
    var tween = new TweenSpline(transform.gameObject, identifier, splineContainer, duration, (position, tangent, upVector, reverse) => {
      transform.position = position;
      if (applyRotation)
        transform.rotation = QuaternionExt.Tangent(reverse ? -tangent : tangent);
    });
    return tween;
  }
  public static TweenDiscrete<float> TweenAlpha(this SpriteRenderer spriteRenderer, float startOpacity, float endOpacity, float duration)
  {
    string identifier = $"{spriteRenderer.GetInstanceID()}.Alpha";
    var tween = new TweenDiscrete<float>(spriteRenderer, identifier, new float[]{ startOpacity, endOpacity }, duration, (float opacity) => {
      spriteRenderer.color = new Color(spriteRenderer.color.r, spriteRenderer.color.g, spriteRenderer.color.b, opacity);
    });
    return tween;
  }
  public static TweenDiscrete<float> Float(float startValue, float endValue, Action<float> UpdateValue, float duration)
  {
    string identifier = $"{UpdateValue.Target.GetHashCode()}.Float";

    var tween = new TweenDiscrete<float>(UpdateValue.Target, identifier, new float[] { startValue, endValue }, duration, val => {
      UpdateValue(val);
    });
    return tween;
  }
  public static TweenSpeed<Vector2> TweenTowardsPosition(this Transform transform, Func<Vector2> getTargetPosition, float speed, bool completable = false)
  {
    var identifier = $"{transform.GetInstanceID()}.TowardsPosition";

    var tween = new TweenSpeed<Vector2>(transform.gameObject, identifier, speed, () => transform.position, getTargetPosition, completable, val => {
      transform.position = val;
    });
    return tween;
  }
  public static TweenSpeed<Vector2> TweenTowardsPosition(this Transform transform, Vector2 targetPosition, float speed, bool completable = false)
  {
    return TweenTowardsPosition(transform, () => targetPosition, speed, completable);
  }
  public static TweenSpeed<Quaternion> TweenTowardsRotation(this Transform transform, Func<Quaternion> getTargetRotation, float speed, bool completable = false)
  {
    var identifier = $"{transform.GetInstanceID()}.TowardsRotation";

    var tween = new TweenSpeed<Quaternion>(transform.gameObject, identifier, speed, () => transform.rotation, getTargetRotation, completable, val => {
      transform.rotation = val;
    });
    return tween;
  }
  public static TweenSpeed<Quaternion> TweenTowardsRotation(this Transform transform, Quaternion targetRotation, float speed, bool completable = false)
  {
    return TweenTowardsRotation(transform, () => targetRotation, speed, completable);
  }
  public static TweenAnim<T> TweenAnim<T>(this GameObject gameObject, T defaultValue = default, bool completable = false) where T : new ()
  {
    var createTimeTrackMethod = typeof(Tween).GetMethod(nameof(CreateTimeTrack), BindingFlags.Static | BindingFlags.NonPublic | BindingFlags.Public);

    List<TweenTrack<T>> tracks = new();

    // var type = typeof(T);
    // var fields = typeof(T).GetFields();
    // foreach (var field in fields)
    // {
    //   var attrs = field.GetCustomAttributes().ToList();
    //   if (attrs.Count == 0)
    //     continue;

    //   var attr = attrs[0];
    //   if (attr is TweenTimeTrackAttribute)
    //   {
    //     var fieldType = field.FieldType;
    //     var method = createTimeTrackMethod.MakeGenericMethod(type, fieldType);
    //     var track = method.Invoke(null, new object[] { field.Name });
    //     tracks.Add((TweenTrack<T>)track);
    //   }
    // }

    string identifier = $"{gameObject.GetInstanceID()}.{typeof(T)}.Tracks";
    var tween = new TweenAnim<T>(gameObject, identifier, defaultValue, tracks.ToArray(), completable);
    return tween;
  }

  private static TweenTimeTrack<T, P> CreateTimeTrack<T, P>(TweenAnim<T> tweenAnim, string fieldName) where T : new()
  {
    return new (tweenAnim, fieldName);
  }

  // public static TweenTimeTrackChange<T> CreateTimeTrackChange<T>(float duration, T[] values, TweenTimeTrackChangeOptions<T> options = null)
  // {
  //   options ??= new TweenTimeTrackChangeOptions<T>();

  //   return new()
  //   {
  //     duration = duration,
  //     values = values,
  //     curve = options.curve,
  //     easeType = options.easeType,
  //     easeFunc = options.easeFunc,
  //     loopCount = options.loopCount,
  //     pingpong = options.pingpong
  //   };
  // }

  // public static TweenTimeTrackChange<T> CreateTimeTrackChange<T>(float duration, T value, TweenTimeTrackChangeOptions<T> options = null)
  // {
  //   return CreateTimeTrackChange(duration, new T[] { value }, options);
  // }

  public static TweenTimeTrackClip<T> CreateTimeTrackClip<T>(
    T[] values, 
    float? duration = null, 
    float? delay = null, 
    EaseType? easeType = null,
    float? speed = null,
    bool? reverse = null,
    int? loopCount = null, 
    bool? pingpong = null,
    AnimationCurve curve = null, 
    Func<float, float> easeFunc = null
  )
  {
    return new ()
    {
      duration = duration,
      delay = delay,
      values = values,
      curve = curve,
      easeType = easeType,
      easeFunc = easeFunc,
      speed = speed,
      reverse = reverse,
      loopCount = loopCount,
      pingpong = pingpong,
    };
  }

  public static TweenTimeTrackClip<T> CreateTimeTrackClip<T>(
    T value, 
    float? duration = null, 
    float? delay = null,
    EaseType? easeType = null,
    float? speed = null,
    bool? reverse = null,
    int? loopCount = null, 
    bool? pingpong = null,
    AnimationCurve curve = null, 
    Func<float, float> easeFunc = null
  )
  {
    return CreateTimeTrackClip(new T[] { value }, duration, delay, easeType, speed, reverse, loopCount, pingpong, curve, easeFunc);
  }

  public static TweenSpeedTrackClip<T> CreateSpeedTrackClip<T>(
    Func<T> GetTargetValue,
    Func<float> GetSpeed,
    Func<T> GetStartValue = null,
    bool? completable = null,
    Func<T, T, float, T> interpolateFunc = null,
    Func<T, T, bool> compareFunc = null
  )
  {
    return new()
    {
      GetStartValue = GetStartValue,
      GetTargetValue = GetTargetValue,
      GetSpeed = GetSpeed,
      completable = completable,
      interpolateFunc = interpolateFunc,
      compareFunc = compareFunc
    };
  }

  public static TweenSpeedTrackClip<T> CreateSpeedTrackClip<T>(
    Func<T> GetTargetValue,
    float speed,
    Func<T> GetStartValue = null,
    bool completable = true,
    Func<T, T, float, T> interpolateFunc = null
  )
  {
    return CreateSpeedTrackClip(GetTargetValue, () => speed, GetStartValue, completable, interpolateFunc);
  }

  // public static TweenSpeedTrackChange<T> CreateSpeedTrackChange<T>(
  //   T targetValue,
  //   float speed,
  //   T startValue = default,
  //   bool completable = true,
  //   Func<T, T, float, T> interpolateFunc = null
  // )
  // {
  //   return CreateSpeedTrackChange(GetTargetValue, () => speed, GetStartValue, completable, interpolateFunc);
  // }

  public static TweenFreeAnimTrackClip CreateFreeAnimTrackClip(
    AnimData animData,
    Func<float> GetTick
  )
  {
    return new()
    {
      animData = animData,
      GetTick = GetTick
    };
  }
  public static TweenAnimSubTrackClip CreateAnimTrackClip(
    AnimData animData,
    int layerIndex = 0,
    float weight = 0f,
    float? crossDuration = null,
    EaseType? crossEaseType = null,
    Func<float, float> crossEaseFunc = null,
    AnimationCurve crossCurve = null,
    float? speed = null,
    bool? reverse = null,
    int? loopCount = null,
    bool? pingpong = null
  )
  {
    return new()
    {
      animData = animData,
      layerIndex = layerIndex,
      weight = weight,
      crossDuration = crossDuration,
      crossEaseType = crossEaseType,
      crossEaseFunc = crossEaseFunc,
      crossCurve = crossCurve,
      speed = speed,
      reverse = reverse,
      loopCount = loopCount,
      pingpong = pingpong
    };
  }

  #region interpolate functions

  #endregion
}