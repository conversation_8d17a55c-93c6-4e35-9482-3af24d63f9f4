

using System;
using System.Collections.Generic;
using UnityEngine;

public class TweenSpeed<T> : TweenBase<TweenSpeed<T>>
{
  private Func<T> _getTargetValue;
  private float _speed;
  private Func<T> _getCurrentValue;
  private Action<T> _onTweenUpdate;

  public TweenSpeed(object target, string identifier, float speed, Func<T> GetCurrentValue, Func<T> GetTargetValue, bool completable, Action<T> onTweenUpdate) : base(target, identifier, completable)
  {
    _onTweenUpdate = onTweenUpdate;
    _getCurrentValue = GetCurrentValue;
    _getTargetValue = GetTargetValue;
    _speed = speed;
  }

  public override void Kill()
  {
    base.Kill();
    _onTweenUpdate = null;
  }

  public override void Update()
  {
    if (!CanUpdate())
      return;

    var currentValue = _getCurrentValue();
    var targetValue = _getTargetValue();

    var nextValue = TweenHelper.InterpolateSpeed(currentValue, targetValue, _speed);

    _onTweenUpdate?.Invoke(nextValue);
    _onUpdate?.Invoke();

    if (Completable && TweenHelper.ValueEqual(nextValue, targetValue))
    {
      OnComplete();
    }
  }
}