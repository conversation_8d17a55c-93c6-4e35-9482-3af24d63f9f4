

using System;
using UnityEngine;
using Tweening;
using UnityEngine.Splines;

public sealed class TweenSpline : TweenTime<TweenSpline>
{
  private SplineContainer _splineContainer;
  private Action<Vector3, Vector3, Vector3, bool> _onTweenUpdateSpline;

  public TweenSpline(object target, string identifier, SplineContainer splineContainer, float duration, Action<Vector3, Vector3, Vector3, bool> onTweenUpdate) : base(target, identifier, duration)
  {
    _splineContainer = splineContainer;
    _onTweenUpdateSpline = onTweenUpdate;
  }

  public override void Update()
  {
    if (!CanUpdate())
      return;

    var t = GetTick();

    _splineContainer.Evaluate(t, out var position, out var tangent, out var upVector);
    _onTweenUpdateSpline?.Invoke(position, tangent, upVector, _reverse);
    
    Tick();
  }

}