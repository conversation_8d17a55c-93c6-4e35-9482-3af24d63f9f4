
using System;
using Tweening;
using UnityEngine;

public static class TweenHelper
{
  public static float Ease(AnimationCurve curve, Func<float, float> easeFunc, EaseType easeType, float t)
  {
    if (curve != null)
    {
      return curve.Evaluate(t);
    }
    else if (easeFunc != null)
    {
      return easeFunc(t);
    }

    switch (easeType)
    {
      case EaseType.Zero:
        return EasingFunctions.Zero(t);
      case EaseType.One:
        return EasingFunctions.One(t);
      case EaseType.Linear:
        return EasingFunctions.Linear(t);
      case EaseType.EaseInQuad:
        return EasingFunctions.InQuad(t);
      case EaseType.EaseOutQuad:
        return EasingFunctions.OutQuad(t);
      case EaseType.EaseInOutQuad:
        return EasingFunctions.InOutQuad(t);
      case EaseType.EaseInCubic:
        return EasingFunctions.InCubic(t);
      case EaseType.EaseOutCubic:
        return EasingFunctions.OutCubic(t);
      case EaseType.EaseInOutCubic:
        return EasingFunctions.InOutCubic(t);
      case EaseType.EaseInQuart:
        return EasingFunctions.InQuart(t);
      case EaseType.EaseOutQuart:
        return EasingFunctions.OutQuart(t);
      case EaseType.EaseInOutQuart:
        return EasingFunctions.InOutQuart(t);
      case EaseType.EaseInQuint:
        return EasingFunctions.InQuint(t);
      case EaseType.EaseOutQuint:
        return EasingFunctions.OutQuint(t);
      case EaseType.EaseInOutQuint:
        return EasingFunctions.InOutQuint(t);
      case EaseType.EaseInSine:
        return EasingFunctions.InSine(t);
      case EaseType.EaseOutSine:
        return EasingFunctions.OutSine(t);
      case EaseType.EaseInOutSine:
        return EasingFunctions.InOutSine(t);
      case EaseType.EaseInExpo:
        return EasingFunctions.InExpo(t);
      case EaseType.EaseOutExpo:
        return EasingFunctions.OutExpo(t);
      case EaseType.EaseInOutExpo:
        return EasingFunctions.InOutExpo(t);
      case EaseType.EaseInCirc:
        return EasingFunctions.InCirc(t);
      case EaseType.EaseOutCirc:
        return EasingFunctions.OutCirc(t);
      case EaseType.EaseInOutCirc:
        return EasingFunctions.InOutCirc(t);
      case EaseType.EaseInElastic:
        return EasingFunctions.InElastic(t);
      case EaseType.EaseOutElastic:
        return EasingFunctions.OutElastic(t);
      case EaseType.EaseInOutElastic:
        return EasingFunctions.InOutElastic(t);
      case EaseType.EaseInBack:
        return EasingFunctions.InBack(t);
      case EaseType.EaseOutBack:
        return EasingFunctions.OutBack(t);
      case EaseType.EaseInOutBack:
        return EasingFunctions.InOutBack(t);
      case EaseType.EaseInBounce:
        return EasingFunctions.InBounce(t);
      case EaseType.EaseOutBounce:
        return EasingFunctions.OutBounce(t);
      case EaseType.EaseInOutBounce:
        return EasingFunctions.InOutBounce(t);
    }

    return t;
  }

  public static T Interpolate<T>(T startValue, T endValue, float t)
  {
    if (startValue is Array startArray && endValue is Array endArray)
    {
      // 确保数组长度相同
      if (startArray.Length != endArray.Length)
        throw new ArgumentException("Arrays must be of the same length.");

      // 创建新数组（动态类型）
      var resultArray = Array.CreateInstance(
          elementType: startArray.GetType().GetElementType(),
          length: startArray.Length);

      for (int i = 0; i < startArray.Length; i++)
      {
        dynamic startItem = startArray.GetValue(i);
        dynamic endItem = endArray.GetValue(i);
        resultArray.SetValue(Interpolate(startItem, endItem, t), i);
      }

      return (T)(object)resultArray;
    }
    else if (startValue is float startValueFloat && endValue is float endValueFloat)
    {
      return (T)(object)Mathf.LerpUnclamped(startValueFloat, endValueFloat, t);
    }
    else if (startValue is Vector3 startValueVector3 && endValue is Vector3 endValueVector3)
    {
      return (T)(object)Vector3.LerpUnclamped(startValueVector3, endValueVector3, t);
    }
    else if (startValue is Quaternion startValueQuaternion && endValue is Quaternion endValueQuaternion)
    {
      return (T)(object)Quaternion.LerpUnclamped(startValueQuaternion, endValueQuaternion, t);
    }
    else if (startValue is Color startValueColor && endValue is Color endValueColor)
    {
      return (T)(object)Color.LerpUnclamped(startValueColor, endValueColor, t);
    }
    else if (startValue is Vector2 startValueVector2 && endValue is Vector2 endValueVector2)
    {
      return (T)(object)Vector2.LerpUnclamped(startValueVector2, endValueVector2, t);
    }
    throw new NotImplementedException();
  }

  public static T Weighted<T>(this T value, float weight)
  {
    if (value is float floatValue)
    {
      return (T)(object)(floatValue * weight);
    }
    else if (value is Vector2 vector2Value)
    {
      return (T)(object)(vector2Value * weight);
    }
    else if (value is Vector3 vector3Value)
    {
      return (T)(object)(vector3Value * weight);
    }
    else if (value is Quaternion quaternionValue)
    {
      return (T)(object)Quaternion.Slerp(Quaternion.identity, quaternionValue, weight);
    }
    else if (value is Color colorValue)
    {
      return (T)(object)Color.Lerp(Color.black, colorValue, weight);
    }
    throw new NotImplementedException();
  }

  public static T Add<T>(T a, T b)
  {
    if (a is float floatA && b is float floatB)
    {
      return (T)(object)(floatA + floatB);
    }
    else if (a is Vector2 vector2A && b is Vector2 vector2B)
    {
      return (T)(object)(vector2A + vector2B);
    }
    else if (a is Vector3 vector3A && b is Vector3 vector3B)
    {
      return (T)(object)(vector3A + vector3B);
    }
    else if (a is Quaternion quaternionA && b is Quaternion quaternionB)
    {
      return (T)(object)(quaternionA * quaternionB);
    }
    else if (a is Color colorA && b is Color colorB)
    {
      return (T)(object)(colorA + colorB);
    }

    throw new NotImplementedException();

  }

  public static float Sum<T>(T[] values)
  {
    float sum = 0f;
    for (int i = 0; i < values.Length - 1; ++i)
    {
      var curr = values[i];
      var next = values[i + 1];
      sum += Distance(curr, next);
    }
    return sum;
  }

  public static float Distance<T>(T first, T second)
  {
    if (first is float firstFloat && second is float secondFloat)
    {
      return Mathf.Abs(firstFloat - secondFloat);
    }
    else if (first is Vector2 firstVector2 && second is Vector2 secondVector2)
    {
      return Vector2.Distance(firstVector2, secondVector2);
    }
    else if (first is Vector3 firstVector3 && second is Vector3 secondVector3)
    {
      return Vector3.Distance(firstVector3, secondVector3);
    }
    else if (first is Quaternion firstQuaternion && second is Quaternion secondQuaternion)
    {
      return Quaternion.Angle(firstQuaternion, secondQuaternion);
    }
    else if (first is Color firstColor && second is Color secondColor)
    {
      return Mathf.Abs(firstColor.r - secondColor.r) + Mathf.Abs(firstColor.g - secondColor.g) + Mathf.Abs(firstColor.b - secondColor.b);
    }
    return 1f;
  }

  public static T InterpolateSpeed<T>(T currentValue, T targetValue, float speed)
  {
    if (currentValue is float currentFloat && targetValue is float targetFloat)
    {
      return (T)(object)Mathf.MoveTowards(currentFloat, targetFloat, speed * Time.deltaTime);
    }
    else if (currentValue is Vector2 currentVector2 && targetValue is Vector2 targetVector2)
    {
      return (T)(object)Vector2.MoveTowards(currentVector2, targetVector2, speed * Time.deltaTime);
    }
    else if (currentValue is Vector3 currentVector3 && targetValue is Vector3 targetVector3)
    {
      return (T)(object)Vector3.MoveTowards(currentVector3, targetVector3, speed * Time.deltaTime);
    }
    else if (currentValue is Quaternion quaternion && targetValue is Quaternion targetQuaternion)
    {
      return (T)(object)Quaternion.RotateTowards(quaternion, targetQuaternion, speed * Time.deltaTime);
    }
    throw new NotImplementedException();
  }

  public static bool ValueEqual<T>(T value1, T value2)
  {
    if (value1 is float floatValue1 && value2 is float floatValue2)
    {
      return Mathf.Approximately(floatValue1, floatValue2);
    }
    else if (value1 is Quaternion quaternion1 && value2 is Quaternion quaternion2)
    {
      return quaternion1.Equals(quaternion2);
    }
    else if (value1 is Vector3 vector3 && value2 is Vector3 vector4)
    {
      return vector3.Equals(vector4);
    }
    else if (value1 is Vector2 vector5 && value2 is Vector2 vector6)
    {
      return vector5.Equals(vector6);
    }
    return false;
  }

}