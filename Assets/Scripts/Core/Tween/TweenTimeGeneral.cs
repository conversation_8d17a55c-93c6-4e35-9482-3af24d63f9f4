

using System;
using UnityEngine;
using Tweening;

public class TweenTimeGeneral<T> : TweenBase<T> where T : TweenTimeGeneral<T>
{
  protected float _elapsedTime = 0f;

  protected int _loopTimes = 0;
  protected int _loopCount = 0;
  protected bool _pingpong = false;
  protected bool _reverse = false;
  protected Action _onLoop;

  public TweenTimeGeneral(object target, string identifier, bool completable) : base(target, identifier, completable)
  {
  }

  protected virtual float Duration { set; get; }

  public int LoopTimes { get => _loopTimes; }

  public virtual void Rewind()
  {
    if (!Completable)
    {
      IsComplete = false;
    }
    _elapsedTime = 0f;
    _reverse = false;
  }

  public override void Kill()
  {
    base.Kill();
    _onLoop = null;
  }

  protected void Tick()
  {
    _onUpdate?.Invoke();

    if (_elapsedTime >= Duration)
    {
      _loopTimes++;
      _onLoop?.Invoke();

      if (_loopTimes >= _loopCount && _loopCount >= 0)
      {
        OnComplete();
      }
      else
      {
        _elapsedTime = 0f;
        if (_pingpong)
        {
          _reverse = !_reverse;
        }
      }
    }
    else
    {
      _elapsedTime += Time.deltaTime;
    }
  }

  protected float Ease(AnimationCurve curve, EaseType easeType, float t, Func<float, float> easeFunc)
  {
    if (curve != null)
    {
      return curve.Evaluate(t);
    }
    else if (easeFunc != null)
    {
      return easeFunc(t);
    }

    switch (easeType)
    {
      case EaseType.Linear:
        return EasingFunctions.Linear(t);
      case EaseType.EaseInQuad:
        return EasingFunctions.InQuad(t);
      case EaseType.EaseOutQuad:
        return EasingFunctions.OutQuad(t);
      case EaseType.EaseInOutQuad:
        return EasingFunctions.InOutQuad(t);
      case EaseType.EaseInCubic:
        return EasingFunctions.InCubic(t);
      case EaseType.EaseOutCubic:
        return EasingFunctions.OutCubic(t);
      case EaseType.EaseInOutCubic:
        return EasingFunctions.InOutCubic(t);
      case EaseType.EaseInQuart:
        return EasingFunctions.InQuart(t);
      case EaseType.EaseOutQuart:
        return EasingFunctions.OutQuart(t);
      case EaseType.EaseInOutQuart:
        return EasingFunctions.InOutQuart(t);
      case EaseType.EaseInQuint:
        return EasingFunctions.InQuint(t);
      case EaseType.EaseOutQuint:
        return EasingFunctions.OutQuint(t);
      case EaseType.EaseInOutQuint:
        return EasingFunctions.InOutQuint(t);
      case EaseType.EaseInSine:
        return EasingFunctions.InSine(t);
      case EaseType.EaseOutSine:
        return EasingFunctions.OutSine(t);
      case EaseType.EaseInOutSine:
        return EasingFunctions.InOutSine(t);
      case EaseType.EaseInExpo:
        return EasingFunctions.InExpo(t);
      case EaseType.EaseOutExpo:
        return EasingFunctions.OutExpo(t);
      case EaseType.EaseInOutExpo:
        return EasingFunctions.InOutExpo(t);
      case EaseType.EaseInCirc:
        return EasingFunctions.InCirc(t);
      case EaseType.EaseOutCirc:
        return EasingFunctions.OutCirc(t);
      case EaseType.EaseInOutCirc:
        return EasingFunctions.InOutCirc(t);
      case EaseType.EaseInElastic:
        return EasingFunctions.InElastic(t);
      case EaseType.EaseOutElastic:
        return EasingFunctions.OutElastic(t);
      case EaseType.EaseInOutElastic:
        return EasingFunctions.InOutElastic(t);
      case EaseType.EaseInBack:
        return EasingFunctions.InBack(t);
      case EaseType.EaseOutBack:
        return EasingFunctions.OutBack(t);
      case EaseType.EaseInOutBack:
        return EasingFunctions.InOutBack(t);
      case EaseType.EaseInBounce:
        return EasingFunctions.InBounce(t);
      case EaseType.EaseOutBounce:
        return EasingFunctions.OutBounce(t);
      case EaseType.EaseInOutBounce:
        return EasingFunctions.InOutBounce(t);
    }

    return t;
  }

  public T SetReverse(bool reverse)
  {
    _reverse = reverse;
    return (T)this;
  }

  public T SetOnLoop(Action onLoop)
  {
    _onLoop = onLoop;
    return (T)this;
  }

  public T SetLoop(int loopCount = 1, bool pingpong = false)
  {
    _loopCount = loopCount;
    _pingpong = pingpong;
    return (T)this;
  }
}