

using System;
using UnityEngine;
using Tweening;

public sealed class TweenDiscrete<T> : TweenTime<TweenDiscrete<T>>
{
  private T[] _positions;
  private float[] _percents;
  private Action<T> _onTweenUpdate;
  private Func<T, T, float, bool, T> _interpolateFunc;

  public TweenDiscrete(object target, string identifier, T[] positions, float duration, Action<T> onTweenUpdate) : base(target, identifier, duration)
  {
    _onTweenUpdate = onTweenUpdate;
    _positions = positions;
    _percents = new float[positions.Length - 1];

    float sum = TweenHelper.Sum(_positions);
    float currentDistance = 0f;
    for (int i = 0; i < _positions.Length - 1; ++i)
    {
      currentDistance += TweenHelper.Distance(_positions[i], _positions[i + 1]);
      _percents[i] = currentDistance / sum;
    }
  }

  public override void Kill()
  {
    base.Kill();
    _onTweenUpdate = null;
  }

  public override void Update()
  {
    if (!CanUpdate())
      return;

    var t = GetTick();

    if (_reverse)
    {
      for (int i = _percents.Length - 1; i >= 0; i--)
      {
        var bottomPercent = 0f;
        if (i != 0)
        {
          bottomPercent = _percents[i - 1];
        }

        if (t >= bottomPercent || (t < 0 && i == 0))
        {
          Interpolate(_positions[i], _positions[i + 1], (t - bottomPercent) / (_percents[i] - bottomPercent), true);
          break;
        }
      }
    }
    else
    {
      for (int i = 0; i < _percents.Length; i++)
      {
        if (t <= _percents[i] || (t > 1.0f && i == _percents.Length - 1))
        {
          float lastPercent = 0f;
          if (i != 0)
          {
            lastPercent = _percents[i - 1];
          }

          Interpolate(_positions[i], _positions[i + 1], (t - lastPercent) / (_percents[i] - lastPercent), false);
          break;
        }
      }
    }

    Tick();
  }

  private void Interpolate(T a, T b, float t, bool reverse)
  {
    if (_interpolateFunc != null)
    {
      _onTweenUpdate?.Invoke(_interpolateFunc.Invoke(a, b, t, reverse));
    }
    else
    {
      _onTweenUpdate?.Invoke(TweenHelper.Interpolate(a, b, t));
    }
  }

  public TweenDiscrete<T> SetInterpolateFunc(Func<T, T, float, bool, T> interpolateFunc)
  {
    _interpolateFunc = interpolateFunc;
    return this;
  }

}