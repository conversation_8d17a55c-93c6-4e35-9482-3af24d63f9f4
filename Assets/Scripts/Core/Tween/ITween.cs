
using System;
using System.Collections.Generic;
using UnityEngine;

public enum EaseType
{
    Zero,
    One,
    Linear,
    EaseInQuad,
    EaseOutQuad,
    EaseInOutQuad,
    EaseInCubic,
    EaseOutCubic,
    EaseInOutCubic,
    EaseInQuart,
    EaseOutQuart,
    EaseInOutQuart,
    EaseInQuint,
    EaseOutQuint,
    EaseInOutQuint,
    EaseInSine,
    EaseOutSine,
    EaseInOutSine,
    EaseInExpo,
    EaseOutExpo,
    EaseInOutExpo,
    EaseInCirc,
    EaseOutCirc,
    EaseInOutCirc,
    EaseInElastic,
    EaseOutElastic,
    EaseInOutElastic,
    EaseInBack,
    EaseOutBack,
    EaseInOutBack,
    EaseInBounce,
    EaseOutBounce,
    EaseInOutBounce
}


public interface ITween
{
  object Target { get; }
  string Identifier { get; }
  bool IsTargetDestroyed { get; }
  bool IsComplete { get; }
  bool Completable { get; }
  bool wasKilled { get; }
  bool IsPaused { get; }
  // event Action OnUpdate;
  // event Action OnComplete;
  Func<ITween> GetNextTween { get; }

  void Update();
  void Kill();
  void Start();
  void Pause();
  void Resume();
  ITween Then(Func<ITween> getTween);
  // void SetEase(EaseType easeType);
  // void SetAnimationCurve(AnimationCurve curve);
}