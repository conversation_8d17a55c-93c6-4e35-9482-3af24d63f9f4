
// using System;
// using System.Linq;
// using Tweening;
// using UnityEngine;

// public delegate R TweenInterpolateFunc<T, R>(T start, T end, float t, bool reverse, TweenSingleTime<T, R> tweenSingleTime);
// public class TweenSingle
// {
//   public float delay = 0f;
//   public float delayedTime = 0f;
//   // public float duration = float.PositiveInfinity;
//   public object updateValue;
// }

// internal interface ITweenSingleTime {
    
// }

// public class TweenSingleTime<T, R> : TweenSingle, ITweenSingleTime
// {
//   public float duration { set; get; }
//   public T[] values;
//   public AnimationCurve curve;
//   public EaseType easeType = EaseType.Linear;
//   public Func<float, float> easeFunc;
//   // public Func<object, object, float, bool, TweenSingleTime, object> InterpolateFunc;
//   public TweenInterpolateFunc<T, R> InterpolateFunc;
//   public float[] percents;
//   public int loopTimes = 0;
//   public int loopCount = 1;
//   public bool reverse = false;
//   public bool pingpong = false;
// }

// public class TweenSingleSpeed : TweenSingle
// {
//   public Func<object> GetCurrentValue;
//   public Func<object> GetStartValue;
//   public Func<object> GetTargetValue;
//   public Func<float> GetSpeed;
// }

// public class TweenDiscreteCompose : TweenTimeGeneral
// {
//   private TweenSingle[] _tweenSingles;
//   private Action<object[]> _onTweenUpdate;

//   public TweenDiscreteCompose(object target, string identifier, TweenSingle[] tweenSingles, Action<object[]> onTweenUpdate) : base(target, identifier)
//   {
//     _onTweenUpdate = onTweenUpdate;
//     _tweenSingles = tweenSingles;

//     foreach (var tweenSingle in _tweenSingles)
//     {
//       if (IsTweenSingleTime(tweenSingle))
//       {
//         dynamic tweenSingleTime = tweenSingle;
//         tweenSingleTime.percents = new float[tweenSingleTime.values.Length - 1];
//         float sum = TweenHelper.Sum(tweenSingleTime.values);
//         // if sum is 0
//         bool useCount = Mathf.Approximately(sum, 0f);
//         if (useCount)
//         {
//           sum = tweenSingleTime.values.Length - 1;
//         }
//         float currentDistance = 0f;
//         for (int i = 0; i < tweenSingleTime.values.Length - 1; i++)
//         {
//           var diff = useCount ? 1f : TweenHelper.Distance(tweenSingleTime.values[i], tweenSingleTime.values[i + 1]);
//           currentDistance += diff;
//           tweenSingleTime.percents[i] = currentDistance / sum;
//         }
//       }
//     }

//   }

//   protected override float Duration
//   {
//     get => _tweenSingles.MaxBy(t => {
//       if (IsTweenSingleTime(t))
//       {
//         dynamic tweenSingleTime = t;
//         return tweenSingleTime.duration * (tweenSingleTime.loopCount >= 0 ? tweenSingleTime.loopCount : float.PositiveInfinity);
//       }
//       return -1;
//     });
//   }

//   public override void Kill()
//   {
//     base.Kill();
//     _onTweenUpdate = null;
//   }

//   bool IsTweenSingleTime(TweenSingle tweenSingle)
//   {
//     var type = tweenSingle.GetType();
//     return type.FullName.Contains("TweenSingleTime");
//   }

//   protected float GetTick<T, K>(TweenSingleTime<T, K> tweenSingleTime)
//   {
//     var elapsedTime = _elapsedTime - tweenSingleTime.duration * tweenSingleTime.loopTimes;
//     var t = Mathf.Clamp01(elapsedTime / tweenSingleTime.duration);
//     if ((_reverse && !tweenSingleTime.reverse) || (!_reverse && tweenSingleTime.reverse))
//     {
//       return Ease(tweenSingleTime.curve, tweenSingleTime.easeType, 1 - t, tweenSingleTime.easeFunc);
//     }
//     return Ease(tweenSingleTime.curve, tweenSingleTime.easeType, t, tweenSingleTime.easeFunc);
//   }
//   protected void TickCompose()
//   {
//     _onUpdate?.Invoke();

//     var tweenSingleTimes = _tweenSingles
//       .Where(x => IsTweenSingleTime(x))
//       .Cast<dynamic>()
//       .ToList();

//     foreach (var tweenSingleTime in tweenSingleTimes)
//     {
//       if (_elapsedTime > tweenSingleTime.duration * (tweenSingleTime.loopTimes + 1))
//       {
//         ++tweenSingleTime.loopTimes;
//         if (tweenSingleTime.pingpong)
//         {
//           tweenSingleTime.reverse = !tweenSingleTime.reverse;
//         }
//       }
//     }

//     var tweenSingleSpeeds = _tweenSingles
//       .Where(x => x is TweenSingleSpeed)
//       .Cast<TweenSingleSpeed>()
//       .ToList();

//     bool reachTargetAll = tweenSingleSpeeds.All(t =>
//     {
//       return TweenHelper.ValueEqual(t.updateValue, t.GetTargetValue());
//     });

//     if (_elapsedTime >= Duration && reachTargetAll)
//     {
//       _loopTimes++;
//       _onLoop?.Invoke();

//       if (_loopTimes >= _loopCount && _loopCount >= 0)
//       {
//         OnComplete();
//       }
//       else
//       {
//         _elapsedTime = 0f;
//         // reset looptimes in singles
//         foreach (var tweenSingleTime in tweenSingleTimes)
//         {
//           tweenSingleTime.loopTimes = 0;
//         }

//         if (_pingpong)
//         {
//           _reverse = !_reverse;
//         }
//       }
//     }
//     else
//     {
//       _elapsedTime += Time.deltaTime;
//     }
//   }

//   public override void Update()
//   {
//     if (!CanUpdate())
//       return;

//     foreach (var tweenSingle in _tweenSingles)
//     {
//       if (tweenSingle.delayedTime < tweenSingle.delay)
//       {
//         tweenSingle.delayedTime += Time.deltaTime;
//         continue;
//       }

//       if (IsTweenSingleTime(tweenSingle))
//       {
//         dynamic tweenSingleTime = tweenSingle;

//         var t = GetTick(tweenSingleTime);
//         var percents = tweenSingleTime.percents;
//         var values = tweenSingleTime.values;

//         if ((_reverse && !tweenSingleTime.reverse) || (!_reverse && tweenSingleTime.reverse))
//         {
//           // only one position
//           if (percents.Length == 0)
//           {
//             Interpolate(tweenSingleTime, values[0], values[0], t, true);
//           }
//           else
//           {
//             for (int i = percents.Length - 1; i >= 0; i--)
//             {
//               var bottomPercent = 0f;
//               if (i != 0)
//               {
//                 bottomPercent = percents[i - 1];
//               }

//               if (t >= bottomPercent || (t < 0 && i == 0))
//               {
//                 Interpolate(tweenSingleTime, values[i], values[i + 1], (t - bottomPercent) / (percents[i] - bottomPercent), true);
//                 break;
//               }
//             }
//           }
//         }
//         else
//         {
//           // only one position
//           if (percents.Length == 0)
//           {
//             Interpolate(tweenSingleTime, values[0], values[0], t, false);
//           }
//           else
//           {
//             for (int i = 0; i < percents.Length; i++)
//             {
//               if (t <= percents[i] || (t > 1.0f && i == percents.Length - 1))
//               {
//                 float lastPercent = 0f;
//                 if (i != 0)
//                 {
//                   lastPercent = percents[i - 1];
//                 }

//                 Interpolate(tweenSingleTime, values[i], values[i + 1], (t - lastPercent) / (percents[i] - lastPercent), false);
//                 break;
//               }
//             }
//           }
//         }
//       }
//       else if (tweenSingle is TweenSingleSpeed tweenSingleSpeed)
//       {
//         var currentValue = tweenSingleSpeed.GetCurrentValue();
//         tweenSingle.updateValue = TweenHelper.InterpolateSpeed(currentValue, _reverse ? tweenSingleSpeed.GetStartValue() : tweenSingleSpeed.GetTargetValue(), tweenSingleSpeed.GetSpeed());
//       }

//     }

//     _onTweenUpdate?.Invoke(Values);

//     TickCompose();
//   }

//   public object[] Values {
//     get {
//       return _tweenSingles.Select(t => t.updateValue).ToArray();
//     }
//   }

//   private void Interpolate<T, K>(TweenSingleTime<T, K> tweenSingleTime, T a, T b, float t, bool reverse)
//   {
//     if (tweenSingleTime.InterpolateFunc != null)
//     {
//       tweenSingleTime.updateValue = tweenSingleTime.InterpolateFunc.Invoke(a, b, t, reverse, tweenSingleTime);
//     }
//     else
//     {
//       tweenSingleTime.updateValue = TweenHelper.Interpolate(a, b, t);
//     }
//   }
// }