
using System;
using System.Collections;
using System.Collections.Generic;
using Unity.Collections;
using Unity.VisualScripting;
using UnityEditorInternal;
using UnityEngine;

[RequireComponent(typeof(Rigidbody2D), typeof(Collider2D))]
public class CharacterBrain<B, M, S> : BaseBrain<B, M, S> where B : CharacterBrain<B, M, S> where M : CharacterStateMachine<B, M, S> where S : CharacterState<B, M, S>
{
    public bool IsJumping { private set; get; }
    public bool IsFalling { private set; get; }
    public bool IsGrounding { private set; get; }
    public Animator Animator { protected set; get; }
    public Rigidbody2D ControllerRigidbody { private set; get; }
    public Collider2D ControllerCollider { private set; get; }
    public AnimationEventManager<B, M, S> AnimEventManager { private set; get; }

    protected SpriteRenderer spriteRenderer;

    protected override void Awake()
    {
        base.Awake();
        ControllerRigidbody = GetComponent<Rigidbody2D>();
        ControllerCollider = GetComponent<Collider2D>();
        Animator = GetComponent<Animator>();
        spriteRenderer = GetComponent<SpriteRenderer>();
        AnimEventManager = new AnimationEventManager<B, M, S>(this);
    }

    protected override void Update()
    {
        UpdateGrounding();
        AnimEventManager.FrameUpdate();
        base.Awake();
    }

    private void UpdateGrounding()
    {
        // var xx = ColliderUtils.GetPositionOf(gameObject, ColliderPosition.BottomCenter);
        // DebugExt.DrawDebugCircle(xx, 0.1f, Color.red);
        // IsGrounding = Physics2D.OverlapCircle(ControllerRigidbody.position, GameConfig.Instance.groundDistance, GameConfig.Instance.groundCheckMask);
        // var circle = ColliderUtils.GetBottomCenterCircle(ControllerRigidbody.position, transform.lossyScale, ControllerCollider);
        // IsGrounding = Physics2D.OverlapCircle(circle.position, circle.radius, GameConfig.Instance.groundMask);

        // var lossyScale = gameObject.transform.lossyScale;
        // var collider = gameObject.GetComponent<Collider2D>();

        // var circle = ColliderUtils.GetBottomCenterCircle(ControllerRigidbody.position, lossyScale, collider);

        // DebugExt.DrawDebugCircle(circle.position, circle.radius, Color.green);
        IsGrounding = GamePhysics.DetectGround(ControllerRigidbody.position, gameObject);
    }

    public void Stop(bool stop)
    {
        Animator.speed = stop ? 0 : 1;
        ControllerRigidbody.simulated = !stop;
    }

    public void TakeDamage(float amount)
    {
        OnDeath();
    }

    public void OnDeath()
    {

    }
}
