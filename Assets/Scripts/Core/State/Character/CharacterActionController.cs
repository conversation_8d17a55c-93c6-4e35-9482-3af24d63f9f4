using System.Collections;
using System.Collections.Generic;
using Unity.Collections;
using Unity.VisualScripting;
using UnityEditorInternal;
using UnityEngine;


public enum CharacterControllerPosition
{
    Grounding,
    Air
}

public abstract class CharacterActionController<B, M, S> : BaseActionController<B, M, S> where B : CharacterBrain<B, M, S> where M : CharacterStateMachine<B, M, S> where S : CharacterState<B, M, S>
{
    public virtual CharacterControllerPosition ControllerPosition { get; protected set; } = CharacterControllerPosition.Grounding;
    protected Rigidbody2D controllerRigidbody;
    protected Collider2D controllerCollider;

    protected override void Awake()
    {
      base.Awake();
      controllerRigidbody = GetComponent<Rigidbody2D>();
      controllerCollider = GetComponent<Collider2D>();
    }

    protected ParticleSystem GenerateEffectOnce(GameObject prefab)
    {
        if (prefab == null) return null;
        
        var instance  = Instantiate(prefab, transform.position, Quaternion.identity);
        if (instance.TryGetComponent(out ParticleSystem effect))
        {
            effect.Emit(1);
            effect.Play();

            Destroy(instance, effect.main.duration * 5);
        }

        return effect;
    }
}

