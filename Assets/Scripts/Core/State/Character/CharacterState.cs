
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using <PERSON>ner;
using UnityEngine;

public class CharacterState<B, M, S> : BaseState<B, M, S> where B : CharacterBrain<B, M, S> where M : CharacterStateMachine<B, M, S> where S : CharacterState<B, M, S>
{
    protected Animator animator {
        get { return stateMachine.brain.Animator; }
    }


    public CharacterState(M stateMachine) : base(stateMachine)
    {
    }

    public override void Enter(S prevState) {
        brain.AnimEventManager.completed += OnAnimFinish;
    }
    public override void Exit() {
        brain.AnimEventManager.completed -= OnAnimFinish;
    }

}

