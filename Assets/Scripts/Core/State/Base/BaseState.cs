
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using <PERSON>ner;
using UnityEngine;

public class BaseState<B, M, S> where B : BaseBrain<B, M, S> where M : BaseStateMachine<B, M, S> where S : BaseState<B, M, S>
{
    public B brain { 
        get { return stateMachine.brain; }
    }

    protected M stateMachine;

    public BaseState(M stateMachine)
    {
        this.stateMachine = stateMachine;
    }

    public virtual void Enter(S prevState) {}
    public virtual void Exit() {}
    public virtual void FrameUpdate() {}
    public virtual void PhysicsUpdate() {}

    public virtual void OnAnimFinish(int hash) { }
    public virtual void OnAnimHalfFinish(int hash) { }

    protected virtual void TransitionTo(S newState)
    {
        if (IsActive())
        {
            stateMachine.TransitionTo(newState);
        }
        else
        {
            Debug.LogWarning($"not current state: {this}");
        }
    }

    public virtual HashSet<BaseActionController<B, M, S>> InitializeControllers()
    {
        return new HashSet<BaseActionController<B, M, S>>();
    }
    public virtual HashSet<BaseActionController<B, M, S>> GetCachedControllers() {
        return new();
    }
    protected void EnableControllers(HashSet<BaseActionController<B, M, S>> controllers) {
        stateMachine.EnableControllers(controllers);
    }
    protected void EnableControllers() {
        EnableControllers(GetCachedControllers());
    }
    protected void DisableControllers(HashSet<BaseActionController<B, M, S>> controllers) {
        stateMachine.DisableControllers(controllers);
    }
    protected void DisableControllers() {
        DisableControllers(GetCachedControllers());
    }

    protected void EnableController(BaseActionController<B, M, S> controller, bool enable = true)
    {
        stateMachine.EnableController(controller, enable);
    }

    protected bool IsActive()
    {
        return stateMachine.CurrentState == this;
    }

}

