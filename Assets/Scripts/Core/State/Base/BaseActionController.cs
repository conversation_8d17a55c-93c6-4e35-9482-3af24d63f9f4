using System.Collections;
using System.Collections.Generic;
using Unity.Collections;
using Unity.VisualScripting;
using UnityEditorInternal;
using UnityEngine;


public abstract class BaseActionController<B, M, S> : MonoBehaviour where B : BaseBrain<B, M, S> where M : BaseStateMachine<B, M, S> where S : BaseState<B, M, S>
{
    protected B brain;
    protected M stateMachine
    {
        get { return brain.StateMachine; }
    }

    protected void TransitionTo(S state)
    {
        stateMachine.TransitionTo(state);
    }
    protected virtual void Awake()
    {
        brain = GetComponent<B>();
        enabled = false;
    }
}

