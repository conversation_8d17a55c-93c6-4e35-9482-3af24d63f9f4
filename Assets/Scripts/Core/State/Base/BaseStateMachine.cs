using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

public class BaseStateMachine<B, M, S> where B : BaseBrain<B, M, S> where M : BaseStateMachine<B, M, S> where S : BaseState<B, M, S>
{
  public B brain { private set; get; }
  private HashSet<BaseActionController<B, M, S>> enabledControllers = new();
  public S CurrentState { private set; get; } 

  public event Action<S> OnStateChanged;

  public BaseStateMachine(B brain)
  {
    this.brain = brain;
  }

  public void Initialize(S initialState)
  {
    CurrentState = initialState;

    TransiteControllers(initialState);

    CurrentState.Enter(null);
    OnStateChanged?.Invoke(CurrentState);
  }

  public virtual void TransitionTo(S newState)
  {
    if (CurrentState == newState) return;
    // Debug.Log($"** State Transition: {CurrentState.GetType().Name} -> {newState.GetType().Name}");

    CurrentState.Exit();

    TransiteControllers(newState);

    var prevState = CurrentState;
    CurrentState = newState;
    CurrentState.Enter(prevState);
    OnStateChanged?.Invoke(CurrentState);
  }

  public void FrameUpdate()
  {
    CurrentState?.FrameUpdate();
  }

  public void PhysicsUpdate()
  {
    CurrentState?.PhysicsUpdate();
  }

  private void TransiteControllers(S nextState)
  {
    var nextEnabledControllers = nextState.InitializeControllers();
    var needDisableControllers = enabledControllers.Except(nextEnabledControllers).ToHashSet();
    var needEnableControllers = nextEnabledControllers.Except(enabledControllers).ToHashSet();

    EnableControllers(needEnableControllers);
    DisableControllers(needDisableControllers);
  }

  // controllers 
  public void EnableControllers(HashSet<BaseActionController<B, M, S>> controllers)
  {
    foreach (BaseActionController<B, M, S> controller in controllers)
    {
      controller.enabled = true;
    }
    this.enabledControllers.UnionWith(controllers);
  }
  public void DisableControllers(HashSet<BaseActionController<B, M, S>> controllers)
  {
    foreach (BaseActionController<B, M, S> controller in controllers)
    {
      controller.enabled = false;
    }
    this.enabledControllers.ExceptWith(controllers);
  }
  public void EnableController(BaseActionController<B, M, S> controller, bool enable = true)
  {
    if (enable)
      enabledControllers.Add(controller);
    else
      enabledControllers.Remove(controller);

    controller.enabled = enable;
  }


}
