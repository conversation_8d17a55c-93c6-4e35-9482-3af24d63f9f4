
using System;
using System.Collections;
using System.Collections.Generic;
using Unity.Collections;
using Unity.VisualScripting;
using UnityEditorInternal;
using UnityEngine;

public enum CharacterDirection
{
    Right,
    Left,
    None
}

public class BaseBrain<B, M, S> : MonoBehaviour where B : BaseBrain<B, M, S> where M : BaseStateMachine<B, M, S> where S : BaseState<B, M, S>
{
    public Vector2 FacingDirection { get { return (FaceToRight && !isFlipped) || (!FaceToRight && isFlipped) ? Vector2.right : Vector2.left; } }

    protected virtual bool FaceToRight
    {
        get { return true; }
    }

    private bool isFlipped
    {
        get
        {
            return (FaceToRight && transform.localScale.x < 0) || (!FaceToRight && transform.localScale.x > 0);
        }
    }

    public M StateMachine { protected set; get; }

    protected virtual void Awake()
    {
        StateMachine = (M)Activator.CreateInstance(typeof(M), new[] { this });
    }

    protected virtual void Update()
    {
        StateMachine.FrameUpdate();
    }

    protected virtual void FixedUpdate()
    {
        StateMachine.PhysicsUpdate();
    }

    public void UpdateDirection(CharacterDirection direction)
    {
        if (direction == CharacterDirection.Right)
        {
            Flip(false);
        }
        else if (direction == CharacterDirection.Left)
        {
            Flip(true);
        }
    }

    public void FlipDirection()
    {
        if (FacingDirection.x > 0)
        {
            Flip(true);
        }
        else if (FacingDirection.x < 0)
        {
            Flip(false);
        }
    }

    protected virtual void Flip(bool toLeft)
    {
        transform.localScale = new Vector3((FaceToRight && toLeft) || (!FaceToRight && !toLeft) ? -Mathf.Abs(transform.localScale.x) : Mathf.Abs(transform.localScale.x), transform.localScale.y, transform.localScale.z);
    }
}
