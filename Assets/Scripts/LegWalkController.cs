
using System;
using System.Collections;
using System.Linq;
using Half;
using Unity.VisualScripting;
using UnityEditor.Rendering;
using UnityEngine;

public enum WalkingLegType
{
  Left,
  Right
}

[System.Serializable]
public class WalkingLeg {
  public WalkingLegType legType;
  [HideInInspector]
  public Transform root;
  public Transform effector;
  public Transform target;
  public float centerLine;
  public float offset;
  [HideInInspector]
  public Vector2? landPosition = null;
  [HideInInspector]
  public Vector2? landOffset = null;
}

public enum HeadlessState
{
  None,
  Idle,
  Chase,
  Aim,
  CancelAim,
  Attack,
  AttackSuccess,
  AttackFail,
}

public class LegWalkController : MonoBehaviour {
  public delegate Vector2 MoveAction(float t);
  public delegate void DoAction(float t);

  [Header("Leg")]
  [SerializeField] WalkingLeg[] walkingLegs;
  [SerializeField] float stepSpacing;
  [SerializeField] float stepDuration;
  // 0.35 for four, 0.5 for two
  [SerializeField] float forwardPercent = 0.5f;
  [SerializeField] float stepHeight;
  [Header("Hip")]
  [SerializeField] Transform hip;
  [SerializeField] Transform HipRoot;
  [SerializeField] float hipSpacing;


  [Header("Idle")]
  [SerializeField] float idleDuration;
  [Header("Attack")]
  [SerializeField] Transform aimLine;
  [SerializeField] Transform aimFarPoint;
  [SerializeField] float aimDuration;
  [SerializeField] AnimationCurve aimCurve;
  [SerializeField] float attackDuration;
  [SerializeField] AnimationCurve attackCurve;
  [SerializeField] Transform toHeadTarget;
  [SerializeField] float toHeadDuration;
  [SerializeField] AnimationCurve toHeadCurve;
  [SerializeField] float toHeadOffset;
  [SerializeField] float beforeReturnDuration;
  [SerializeField] float returnDuration;
  [SerializeField] float minDistanceOfChasePoint;
  [SerializeField] AnimationCurve returnCurve;
  [SerializeField] float aimSpeed;
  [SerializeField] float attackSpeed;

  [Header("Other")]
  [SerializeField] LayerMask groundLayerMask;


  Vector2 FacingDirection = Vector2.left;

  AnimationCurve stepCurve = new();
  AnimationCurve verticalMovingCurve = new();
  AnimationCurve hipCurve = new();

  private float time = 0;

  private HeadlessState currentState = HeadlessState.None;

  private HeadlessState nextState = HeadlessState.None;

  private WalkingLeg currentAttackLeg;
  // private Vector2 attackStartPosition;
  private Transform attackTarget;

  void Awake()
  {
    CalculateStepCurve();
    CalculateVerticalCurve();
    CalculateHipCurve();
  }

  void OnDrawGizmos()
  {
    // leg move range
    foreach(var leg in walkingLegs)
    {
      var hit = Physics2D.Raycast(leg.root.position, Vector2.down, 10f, groundLayerMask);
      Gizmos.color = Color.cyan;
      Gizmos.DrawSphere(hit.point, 0.1f);
      GizmosExt.DrawLine(hit.point - 0.5f * stepSpacing * Vector2.right , hit.point + 0.5f * stepSpacing * Vector2.right, Color.yellow);
    }

    // hip
    Gizmos.color = Color.cyan;
    Gizmos.DrawSphere(HipRoot.position, 0.1f);
    GizmosExt.DrawLine((Vector2)HipRoot.position - 0.5f * hipSpacing * Vector2.up, (Vector2)HipRoot.position + 0.5f * hipSpacing * Vector2.up, Color.yellow);

    // aim line & aim far point
    Gizmos.color = Color.yellow;
    Gizmos.DrawLine((Vector2)aimLine.position - 10f * Vector2.right, (Vector2)aimLine.position + 10f * Vector2.right);
    Gizmos.color = Color.cyan;
    Gizmos.DrawSphere(aimFarPoint.position, 0.2f);

    if (attackTarget != null)
    {
      Gizmos.color = Color.red;
      Gizmos.DrawSphere(aimPoint, 0.2f);
    }

  }

  void CalculateStepCurve()
  {
    Keyframe key1 = new(0f, -1f)
    {
      inTangent = 0f,
      outTangent = 0f
    };

    Keyframe key2 = new(forwardPercent, 1f)
    {
      inTangent = 0f,
      outTangent = (-1f - 1f) / (1f - forwardPercent)
    };


    Keyframe key3 = new(1f, -1f)
    {
      inTangent = key2.outTangent,
      outTangent = 0f,
    };

    stepCurve.AddKey(key1);
    stepCurve.AddKey(key2);
    stepCurve.AddKey(key3);

    stepCurve.preWrapMode = WrapMode.Loop;
    stepCurve.postWrapMode = WrapMode.Loop;
  }

  void CalculateVerticalCurve()
  {
    Keyframe key1 = new(0f, 0f)
    {
      inTangent = 0f,
      outTangent = 0f
    };

    Keyframe key2 = new(forwardPercent / 2, 1f)
    {
      inTangent = 0f,
      outTangent = 0f
    };


    Keyframe key3 = new(forwardPercent, 0f)
    {
      inTangent = 0f,
      outTangent = 0f,
    };

    Keyframe key4 = new(1f, 0f)
    {
      inTangent = 0f,
      outTangent = 0f,
    };

    verticalMovingCurve.AddKey(key1);
    verticalMovingCurve.AddKey(key2);
    verticalMovingCurve.AddKey(key3);
    verticalMovingCurve.AddKey(key4);

    verticalMovingCurve.preWrapMode = WrapMode.Loop;
    verticalMovingCurve.postWrapMode = WrapMode.Loop;
  }

  void CalculateHipCurve()
  {
    Keyframe key1 = new(0f, -1f)
    {
      inTangent = 0f,
      outTangent = 0f
    };

    Keyframe key2 = new(0.25f, 1f)
    {
      inTangent = 0f,
      outTangent = 0f
    };


    Keyframe key3 = new(0.5f, -1f)
    {
      inTangent = 0f,
      outTangent = 0f,
    };

    Keyframe key4 = new(0.75f, 1f)
    {
      inTangent = 0f,
      outTangent = 0f,
    };

    Keyframe key5 = new(1f, -1f)
    {
      inTangent = 0f,
      outTangent = 0f,
    };

    hipCurve.AddKey(key1);
    hipCurve.AddKey(key2);
    hipCurve.AddKey(key3);
    hipCurve.AddKey(key4);
    hipCurve.AddKey(key5);

    hipCurve.preWrapMode = WrapMode.Loop;
    hipCurve.postWrapMode = WrapMode.Loop;
  }

  void Update()
  {
    // if (currentState == HeadlessState.Chase || currentState == HeadlessState.MoveStopping)
    // {
    //   Move();
    // }

    if (Input.GetKeyDown(KeyCode.N))
    {
      if (currentState == HeadlessState.Attack) return;

      currentState = HeadlessState.Attack;
      StartCoroutine(Attack());
    }
  }

  public void Play(HeadlessState state)
  {
    if (state == currentState)
    {
      nextState = HeadlessState.None;
      return;
    }

    if (state == nextState)
      return;

    nextState = state;
    if (currentState == HeadlessState.None)
    {
      ToNextState();
    }
  }

  IEnumerator Idle()
  {
    yield return StartCoroutine(DoContinuous(idleDuration, t => {
      var hipPosition = hip.localPosition;
      hipPosition.y = HipRoot.localPosition.y + hipCurve.Evaluate(t) * hipSpacing * 0.5f;
      hip.localPosition = hipPosition;
    }));

    ToNextState();
  }

  IEnumerator Attack()
  {
    var minXLeg = walkingLegs.Aggregate((minLeg, nextLeg) => 
    nextLeg.target.position.x < minLeg.target.position.x ? nextLeg : minLeg);

    var maxXLeg = walkingLegs.Aggregate((maxLeg, nextLeg) => 
    nextLeg.target.position.x > maxLeg.target.position.x ? nextLeg : maxLeg);

    currentAttackLeg = FacingDirection.x > 0 ? maxXLeg : minXLeg;
    var attackStartPosition = currentAttackLeg.target.position;




    // move to aim target
    // Vector2 aimPosition = aimTarget.position;
    // var distance = (attackTarget.position.x - aimPosition.x) * FacingDirection.x;
    // if (distance < minDistanceOfChasePoint)
    // {
    //   aimPosition -= minDistanceOfChasePoint * FacingDirection;
    // }

    // yield return StartCoroutine(MoveContinuous(
    //   attackStartPosition, aimTarget.position, aimCurve, aimDuration
    // ));
    

    yield return StartCoroutine(DoAim());
    yield return StartCoroutine(DoAttack());

    // // move to attack ray
    // yield return StartCoroutine(MoveContinuous(
    //   aimTarget.position, attackTargetPosition, attackCurve, attackDuration
    // ));

    // // move to head
    // var tipPosition = currentAttackLeg.effector.position;
    // yield return StartCoroutine(MoveContinuous(
    //   toHeadDuration, t => {
    //     return Vector2.Lerp(tipPosition, toHeadTarget.position, toHeadCurve.Evaluate(t)) + Mathf.Sin(t * Mathf.PI) * toHeadOffset * Vector2.up;
    //   }
    // ));

    // yield return new WaitForSeconds(beforeReturnDuration);

    // // move to start position
    // yield return StartCoroutine(MoveContinuous(
    //   returnDuration, t => {
    //     return Vector2.Lerp(toHeadTarget.position, attackStartPosition, returnCurve.Evaluate(t)) + Mathf.Sin(t * Mathf.PI) * toHeadOffset * Vector2.up;
    //   }
    //   // toHeadTarget.position, attackStartPosition, returnCurve, returnDuration
    // ));

    ToNextState();
  }

  IEnumerator DoAttack()
  {
    Vector2 direction = (attackTarget.position - aimFarPoint.position).normalized;
    Vector2 attackTargetPosition = (Vector2)attackTarget.position + direction * 10f;
    var hit = Physics2D.Raycast(attackTarget.position, direction, 10f, groundLayerMask);
    if (hit.collider != null)
    {
      attackTargetPosition = hit.point;
    }

    while (
      Vector2.Distance(currentAttackLeg.target.position, currentAttackLeg.effector.position) < 0.001f &&
      Vector2.Distance(currentAttackLeg.target.position, attackTargetPosition) > 0.001f
    )
    {
      currentAttackLeg.target.position = Vector2.MoveTowards(currentAttackLeg.target.position, attackTargetPosition, Time.deltaTime * attackSpeed);

      yield return null;
    }

  }

  IEnumerator DoAim()
  {
    var time = 0f;
    while (time < aimDuration)
    {
      currentAttackLeg.target.position = Vector2.MoveTowards(currentAttackLeg.target.position, aimPoint, Time.deltaTime * aimSpeed);
      yield return null;
      time += Time.deltaTime;
    }
  }

  IEnumerator Chase()
  {
    yield return StartCoroutine(DoContinuous(stepDuration, t => {
      transform.position += stepSpacing / (stepDuration * (1 - forwardPercent)) * Time.deltaTime * (Vector3)FacingDirection;

      for (int i = 0; i < walkingLegs.Length; ++i)
      {
        var offsetX = stepCurve.Evaluate(t + walkingLegs[i].offset) * stepSpacing * 0.5f;
        var position = new Vector2(offsetX * FacingDirection.x + walkingLegs[i].root.position.x, walkingLegs[i].root.position.y);

        var hit = Physics2D.Raycast(position, Vector2.down, 10f, GameConfig.Instance.groundMask);
        if (hit.collider != null)
        {
          var localPosition = transform.InverseTransformPoint(hit.point);
          var verticalOffset = verticalMovingCurve.Evaluate(t + walkingLegs[i].offset) * stepHeight;
          localPosition.y += verticalOffset;
          walkingLegs[i].target.localPosition = localPosition;
        }
      }

      var hipPosition = hip.localPosition;
      hipPosition.y = HipRoot.localPosition.y + hipCurve.Evaluate(t) * hipSpacing * 0.5f;
      hip.localPosition = hipPosition;
    }));

    ToNextState();
  }

  // IEnumerator DoContinousBySpeed(float speed)
  // {

  // }

  IEnumerator MoveContinuous(Vector2 startPosition, Vector2 endPosition, AnimationCurve curve, float duration)
  {
    float time = 0f;
    while (time < duration)
    {
      var t = time / duration;
      currentAttackLeg.target.position = Vector2.Lerp(startPosition, endPosition, curve.Evaluate(t));
      yield return null;
      time += Time.deltaTime;
    }
  }

  IEnumerator MoveContinuous(float duration, MoveAction action)
  {
    float time = 0f;
    while (time < duration)
    {
      var t = time / duration;
      currentAttackLeg.target.position = action(t);
      yield return null;
      time += Time.deltaTime;
    }
  }

  IEnumerator DoContinuous(float duration, DoAction action)
  {
    float time = 0f;
    while (time < duration)
    {
      var t = time / duration;
      action(t);
      yield return null;
      time += Time.deltaTime;
    }
  }

  public void SetAttackTarget(Transform transform)
  {
    attackTarget = transform;
  }
  void Move()
  {
    transform.position += stepSpacing / (stepDuration * (1 - forwardPercent)) * Time.deltaTime * (Vector3)FacingDirection;
    // Debug.Log($"{leg.localPosition}, {leg.position}");
    time += Time.deltaTime;
    var p = time / stepDuration;

    for (int i = 0; i < walkingLegs.Length; ++i)
    {
      var offsetX = stepCurve.Evaluate(p + walkingLegs[i].offset) * stepSpacing * 0.5f;
      var position = new Vector2(offsetX * FacingDirection.x + walkingLegs[i].root.position.x, walkingLegs[i].root.position.y);

      var hit = Physics2D.Raycast(position, Vector2.down, 10f, GameConfig.Instance.groundMask);
      if (hit.collider != null)
      {
        var localPosition = transform.InverseTransformPoint(hit.point);
        var verticalOffset = verticalMovingCurve.Evaluate(p + walkingLegs[i].offset) * stepHeight;
        localPosition.y += verticalOffset;
        walkingLegs[i].target.localPosition = localPosition;
      }
    }

    var hipPosition = hip.localPosition;
    hipPosition.y = HipRoot.localPosition.y + hipCurve.Evaluate(p) * hipSpacing * 0.5f;
    hip.localPosition = hipPosition;

    var frac = p - Mathf.Floor(p);
    var roundedFrac = Mathf.Round(frac * 100) / 100;
    bool isGrounded = Mathf.Approximately(roundedFrac, 0f) || Mathf.Approximately(roundedFrac, 0.5f);

    if (isGrounded)
    {
      ToNextState();
    }

  }

  void ToNextState()
  {
    if (nextState != HeadlessState.None)
    {
      currentState = nextState;
      nextState = HeadlessState.None;
    }
    else
    {
      // stay current
    }

    if (currentState == HeadlessState.Attack)
    {
      StartCoroutine(Attack());
    }
    else if (currentState == HeadlessState.Idle)
    {
      StartCoroutine(Idle());
    }
    else if (currentState == HeadlessState.Chase)
    {
      StartCoroutine(Chase());
    }
  }


  public Vector2 aimPoint
  {
    get {
      var v1 = attackTarget.position;
      var v2 = aimFarPoint.position;
      var y = aimLine.position.y;

      if (Mathf.Approximately(v1.x, v2.x))
        return new(v1.x, y);

      var x = (y - v2.y) * (v1.x - v2.x) / (v1.y - v2.y) + v2.x;
      return new (x, y);
    }
  }

  // void CalculateHipCurve()
  // {
  //   Keyframe key1 = new(0f, -1f)
  //   {
  //     inTangent = 0f,
  //     outTangent = 0f
  //   };

  //   Keyframe key2 = new(forwardPercent / 2, 1f)
  //   {
  //     inTangent = 0f,
  //     outTangent = 0f
  //   };


  //   Keyframe key3 = new(forwardPercent, -1f)
  //   {
  //     inTangent = 0f,
  //     outTangent = 0f,
  //   };

  //   Keyframe key4 = new(forwardPercent + (1 - forwardPercent) / 2, 1f)
  //   {
  //     inTangent = 0f,
  //     outTangent = 0f,
  //   };

  //   Keyframe key5 = new(1f, -1f)
  //   {
  //     inTangent = 0f,
  //     outTangent = 0f,
  //   };

  //   hipCurve.AddKey(key1);
  //   hipCurve.AddKey(key2);
  //   hipCurve.AddKey(key3);
  //   hipCurve.AddKey(key4);
  //   hipCurve.AddKey(key5);

  //   hipCurve.preWrapMode = WrapMode.Loop;
  //   hipCurve.postWrapMode = WrapMode.Loop;
  // }


}