using UnityEngine;

public class CatenaryCurve : MonoBehaviour
{
    public Transform pointA;
    public Transform pointB;
    public float lengthM;
    public int resolution = 50;
    public float minDistance = 0.01f; // 最小有效距离

    private LineRenderer lineRenderer;

    void Start()
    {
        lineRenderer = GetComponent<LineRenderer>();
        UpdateCatenary();
    }

    void Update()
    {
        if (pointA.hasChanged || pointB.hasChanged)
        {
            UpdateCatenary();
        }
    }

    void UpdateCatenary()
    {
        Vector3 delta = pointB.position - pointA.position;
        float L = delta.magnitude;

        // 处理距离过小的情况
        if (L < minDistance)
        {
            DrawStraightLine();
            return;
        }

        // 检查长度有效性
        if (lengthM <= L)
        {
            Debug.LogWarning("悬链线长度必须大于两点距离！");
            DrawStraightLine();
            return;
        }

        // 解方程求 a，并检查数值稳定性
        float a = SolveForA(L, lengthM);
        Debug.Log($"a: {a}");
        if (float.IsNaN(a) || float.IsInfinity(a))
        {
            DrawStraightLine();
            return;
        }

        // 生成悬链线点
        Vector3[] points = new Vector3[resolution];
        bool hasNaN = false;
        for (int i = 0; i < resolution; i++)
        {
            float t = i / (float)(resolution - 1);
            float x = t * L;
            // (x - L/2)：将悬链线对称中心移动到 x = L/2（即两点中间）。
            // 2. 垂直偏移修正-> 确保悬链线的两端高度为 0（即 y(0) = y(L) = 0）。
            float y = a * (float)System.Math.Cosh((x - L / 2) / a) - a * (float)System.Math.Cosh(L / (2 * a));
            points[i] = new Vector3(x, y, 0);

            // 检查 NaN
            if (float.IsNaN(points[i].x) || float.IsNaN(points[i].y))
            {
                hasNaN = true;
                break;
            }
        }

        // 如果存在 NaN，回退到直线
        if (hasNaN)
        {
            DrawStraightLine();
            return;
        }

        // 旋转到两点连线方向
        Quaternion rotation = Quaternion.FromToRotation(Vector3.right, delta.normalized);
        for (int i = 0; i < resolution; i++)
        {
            points[i] = pointA.position + rotation * points[i];
        }

        // 强制匹配端点
        points[0] = pointA.position;
        points[resolution - 1] = pointB.position;

        // 设置 LineRenderer
        lineRenderer.positionCount = resolution;
        lineRenderer.SetPositions(points);
    }

    // 绘制直线
    void DrawStraightLine()
    {
        lineRenderer.positionCount = 2;
        lineRenderer.SetPosition(0, pointA.position);
        lineRenderer.SetPosition(1, pointB.position);
    }

    float SolveForA(float L, float M)
    {
        // 重新参数化：k = L / (2a)，解 g(k) = sinh(k)/k - M/L = 0
        float targetRatio = M / L;
        
        // 初始猜测 k0（根据 M/L 的合理范围）
        float k = Mathf.Log(2 * targetRatio); // 当 k 较大时，sinh(k)/k ≈ e^k / (2k)
        float tolerance = 1e-6f;
        int maxIterations = 100;

        for (int i = 0; i < maxIterations; i++)
        {
            float sinhK = (Mathf.Exp(k) - Mathf.Exp(-k)) / 2;
            float coshK = (Mathf.Exp(k) + Mathf.Exp(-k)) / 2;
            float g = sinhK / k - targetRatio;
            float dg = (k * coshK - sinhK) / (k * k);

            // 牛顿迭代更新
            float delta = g / dg;
            k -= delta;

            // 防止 k 过小（k > 0）
            k = Mathf.Max(k, 1e-3f);

            if (Mathf.Abs(delta) < tolerance)
                break;
        }

        // 恢复 a = L / (2k)
        return L / (2 * k);
    }

    // // 牛顿迭代法（带保护）
    // float SolveForA(float L, float M)
    // {
    //     float a = Mathf.Max(L / 2, 0.01f); // 初始猜测 + 最小值保护
    //     float tolerance = 0.0001f;
    //     int maxIterations = 100;

    //     for (int i = 0; i < maxIterations; i++)
    //     {
    //         float sinhTerm, coshTerm;
    //         try
    //         {
    //             float arg = L / (2 * a);
    //             sinhTerm = (Mathf.Exp(arg) - Mathf.Exp(-arg)) / 2;
    //             coshTerm = (Mathf.Exp(arg) + Mathf.Exp(-arg)) / 2;
    //         }
    //         catch
    //         {
    //             return float.NaN; // 数值溢出时返回 NaN
    //         }

    //         float f = 2 * a * sinhTerm - M;
    //         float df = 2 * sinhTerm - (L / a) * coshTerm;

    //         // 检查除零或 NaN
    //         if (Mathf.Abs(df) < 1e-10 || float.IsNaN(f) || float.IsNaN(df))
    //         {
    //             return float.NaN;
    //         }

    //         a -= f / df;
    //         a = Mathf.Max(a, 0.01f); // 限制最小值

    //         if (Mathf.Abs(f) < tolerance)
    //             break;
    //     }

    //     return a;
    // }
}