
using UnityEngine;

[ExecuteInEditMode]
public class Tail2 : MonoBehaviour {
  [SerializeField] int count;
  [SerializeField] float distance;
  LineRenderer lineRenderer;

  void Awake()
  {
    lineRenderer = GetComponent<LineRenderer>();
    lineRenderer.positionCount = count;
    var startPosition = transform.position;

    for (int i = 0; i < count; i++)
    {
      lineRenderer.SetPosition(i, new Vector2(startPosition.x + distance * i, startPosition.y));
    }
  }

  void Update()
  {
    lineRenderer.SetPosition(0, transform.position);

    for (int i = 1; i < count; i++)
    {
      var prevPosition = lineRenderer.GetPosition(i - 1);
      var position = lineRenderer.GetPosition(i);
      var dir = (position - prevPosition).normalized;
      lineRenderer.SetPosition(i, prevPosition + dir * distance);
    }
  }
}