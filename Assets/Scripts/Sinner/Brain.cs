
using UnityEngine;

namespace Sinner
{
  public class Brain : CharacterBrain<Brain, StateMachine, State>, IDamagable, IDisturber
  {
    [SerializeField] float ascendGravityScale = 0.0f;
    [SerializeField] float descendGravityScale = 0.0f;
    [SerializeField] float maxVerticalVelocity = 0.0f;

    [Layer]
    [SerializeField] int deathLayer;
    [SerializeField] GameObject poop;

    [Header("Targets")]
    [SerializeField] Transform headTarget;

    void Start()
    {
      StateMachine = new StateMachine(this);
      StateMachine.Initialize(StateMachine.idleState);
    }

    protected override void Update()
    {
      base.Update();

      var circle = ColliderUtils.GetBottomCenterCircle(ControllerRigidbody.position, transform.lossyScale, ControllerCollider);
      DebugExt.DrawDebugCircle(circle.position, circle.radius, Color.green);

      // if (Input.GetKeyDown(KeyCode.N))
      // {
      //   ControllerRigidbody.velocity = Vector2.left * 100f;
      // }
    }

    public void AddPoop(Vector2 point, Vector2 normal)
    {
      poop.GetComponent<PoopHeadController>().AddPoop(point, normal);
    }

    protected override void FixedUpdate()
    {
      base.FixedUpdate();

      ControllerRigidbody.gravityScale = ControllerRigidbody.velocity.y < 0 ? descendGravityScale : ascendGravityScale;
      var velocity = ControllerRigidbody.velocity;
      velocity.y = Mathf.Clamp(velocity.y, -maxVerticalVelocity, maxVerticalVelocity);
      ControllerRigidbody.velocity = velocity;
    }

    public void UpdateDirection()
    {
      if (UserInput.Instance.MoveInput.x > 0)
      {
        Flip(false);
      }
      else if (UserInput.Instance.MoveInput.x < 0)
      {
        Flip(true);
      }
    }

    public void TakeDamage(DamagerType type, Vector3 direction, float force)
    {
      // gameObject.layer = deathLayer;
      StateMachine.TransitionTo(StateMachine.deathState);
      StateMachine.deathState.DieBy(type, direction);
      ControllerRigidbody.AddForce(direction * force, ForceMode2D.Impulse);
    }

    public bool CanTakeDamage()
    {
      return StateMachine.CurrentState != StateMachine.deathState;
    }

    public Transform GetDisturbTarget(DisturbableType type)
    {
      switch (type)
      {
        case DisturbableType.Headless:
          return headTarget;
      }
      return transform;
    }
  }
}