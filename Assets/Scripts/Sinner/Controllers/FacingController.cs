using System.Collections;
using System.Collections.Generic;
using Unity.Collections;
using Unity.VisualScripting;
using UnityEditorInternal;
using UnityEngine;
using UnityEngine.InputSystem;


namespace Sinner {
public class FacingController : ActionController
{
    public static FacingController Instance;

    protected override void Awake()
    {
      base.Awake();
      if (Instance == null)
      {
        Instance = this;
      }
    }

    void Update()
    {
      brain.UpdateDirection();
    }

    void FixedUpdate()
    {
    }
}

}
