using System.Collections;
using System.Collections.Generic;
using Unity.Collections;
using Unity.VisualScripting;
using UnityEditorInternal;
using UnityEngine;
using UnityEngine.InputSystem;


namespace Sinner
{
    public class MoveController : ActionController
    {
        [SerializeField] float walkSpeed = 2.0f;
        [SerializeField] float runSpeed = 10.0f;
        [SerializeField] float maxMoveAngle = 45.0f;
        [SerializeField] ContactFilter2D contactWallFilter;
        public static MoveController Instance;

        protected override void Awake()
        {
            base.Awake();
            if (Instance == null)
            {
                Instance = this;
            }
        }

        void Update()
        {
        }

        void FixedUpdate()
        {
            // if (Mathf.Approximately(UserInput.Instance.MoveInput.x, 0f))
            // {
            //     return;
            // }

            Vector2 targetVelocity = new(UserInput.Instance.MoveInput.x * runSpeed, controllerRigidbody.velocity.y);


            // cast a right ray from the collider bottom right corner
            if (targetVelocity.x > 0f)
            {
                RaycastHit2D[] hits = new RaycastHit2D[1];
                int hitCount = brain.ControllerCollider.Cast(Vector2.right, contactWallFilter, hits, 0.1f);
                if (hitCount > 0)
                {
                    var degree = Vector2.Angle(hits[0].normal, Vector2.up);
                    if (degree > maxMoveAngle)
                    {
                    targetVelocity.x = 0;
                    }
                }
            }
            else if (targetVelocity.x < 0f)
            {
                RaycastHit2D[] hits = new RaycastHit2D[1];
                int hitCount = brain.ControllerCollider.Cast(Vector2.left, contactWallFilter, hits, 0.1f);
                if (hitCount > 0)
                {
                    var degree = Vector2.Angle(hits[0].normal, Vector2.up);
                    if (degree > maxMoveAngle)
                    {
                        targetVelocity.x = 0;
                    }
                }
            }

            controllerRigidbody.velocity = targetVelocity;
        }
    }

}
