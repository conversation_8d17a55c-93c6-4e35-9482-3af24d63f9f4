using System.Collections;
using System.Collections.Generic;
using Unity.Collections;
using Unity.VisualScripting;
using UnityEditorInternal;
using UnityEngine;
using UnityEngine.InputSystem;


namespace Sinner
{
  public class JumpController : ActionController
  {

    [SerializeField] float jumpForce = 0.0f;
    [SerializeField] float coyoteTime = 0.0f;
    [SerializeField] float jumpSpeed = 0.0f;

    private bool isJumping = false;

    private float coyoteTimeCounter = 0.0f;

    private float ascendTime = 0.0f;
    private float time = 0.0f;

    private Vector2 landVelocity;

    public static JumpController Instance;
    protected override void Awake()
    {
      base.Awake();
      if (Instance == null)
      {
        Instance = this;
      }
    }

    void Update()
    {
      if (brain.IsGrounding)
      {
        coyoteTimeCounter = coyoteTime;
      }
      else
      {
        coyoteTimeCounter -= Time.deltaTime;
      }

      if (Mathf.Approximately(controllerRigidbody.velocity.y, 0f))
      {
        ascendTime = Time.time;
      }
      else if (controllerRigidbody.velocity.y > 0f)
      {
        var diff = Time.time - ascendTime;
        // Debug.Log($"xx: {diff}");
      }


      if (controllerRigidbody.velocity.y >= 0)
      {
        time = Time.time;
      }
      else if (brain.IsGrounding)
      {
        var diff = Time.time - time;
        // Debug.Log($"diff {diff}");
      }

    }

    void FixedUpdate()
    {
      if (coyoteTimeCounter > 0 && UserInput.Instance.JumpPressed && !isJumping)
      {
        Debug.Log("trigger jump");
        isJumping = true;
        if (!Mathf.Approximately(UserInput.Instance.MoveInput.x, 0f))
        {
          DoJump();
          stateMachine.TransitionTo(stateMachine.jumpForwardAscendState);
        }
        else
        {
          controllerRigidbody.velocity = Vector2.zero;
          coyoteTimeCounter = 0;
          stateMachine.TransitionTo(stateMachine.jumpStartState);
        }
      }

      // apply velocity if same direction 
      if (stateMachine.CurrentState is JumpForwardAscendState || stateMachine.CurrentState is JumpForwardDescendState && isJumping)
      {
        if (brain.FacingDirection.x * UserInput.Instance.MoveInput.x > 0f)
        {
        Debug.Log("xxx");
          controllerRigidbody.velocity = new (UserInput.Instance.MoveInput.x * jumpSpeed, controllerRigidbody.velocity.y);
        }
      }
    }
    public void DoJump()
    {
      // reset jump speed
      controllerRigidbody.velocity = Vector2.zero;

      var hit = Physics2D.Raycast(ColliderUtils.GetPositionOf(gameObject, ColliderPosition.BottomCenter), Vector2.down, 0.1f, GameConfig.Instance.groundMask);
      if (hit.collider != null)
      {
        // rope
        if (hit.collider is EdgeCollider2D)
        {
          controllerRigidbody.velocity = Vector2.Reflect(landVelocity, hit.normal);
          Debug.Log($"Initial velocity: {controllerRigidbody.velocity}");
        }
      }

      Vector3 addedForce = Vector2.up * jumpForce;

      controllerRigidbody.AddForce(addedForce, ForceMode2D.Impulse);

      time = Time.time;
    }

    public void DoLand()
    {
      if (isJumping)
      {
        Debug.Log("Land");
        landVelocity = controllerRigidbody.velocity;
        isJumping = false;
      }
    }
  }
}
