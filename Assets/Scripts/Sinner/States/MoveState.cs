
using System.Collections.Generic;
using UnityEngine;

namespace Sinner
{
  public class MoveState : State
  {

    private readonly int walkHash = Animator.StringToHash("Walk2");
    private readonly int runHash = Animator.StringToHash("Run");

    public MoveState(StateMachine stateMachine) : base(stateMachine)
    {
    }
    public override HashSet<BaseActionController<Brain, StateMachine, State>> InitializeControllers()
    {
      return new HashSet<BaseActionController<Brain, StateMachine, State>>
          {
            FacingController.Instance,
            MoveController.Instance,
            JumpController.Instance,
            // TransmitController.Instance,
            // DodgeController.Instance,
            // JumpController.Instance,
            // AttackComboAController.Instance
          };
    }

    public override void Enter(State state)
    {
      base.Enter(state);
      animator.CrossFade(runHash, .1f);
      // animator.Play(walkHash);
    }


    public override void FrameUpdate()
    {
      if (!brain.IsGrounding)
      {
        stateMachine.TransitionTo(stateMachine.jumpDescendState);
      }
      else if (Mathf.Approximately(UserInput.Instance.MoveInput.x, 0f))
      {
        stateMachine.TransitionTo(stateMachine.idleState);
      }
    }
  }
}