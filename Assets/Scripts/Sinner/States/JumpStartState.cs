
using System.Collections.Generic;
using UnityEngine;

namespace Sinner
{
  public class JumpStartState : State
  {
    private readonly int jumpStartHash = Animator.StringToHash("Jump_Start");

    public JumpStartState(StateMachine stateMachine) : base(stateMachine)
    {
    }
    public override HashSet<BaseActionController<Brain, StateMachine, State>> InitializeControllers()
    {
      return
          new HashSet<BaseActionController<Brain, StateMachine, State>>
          {
            JumpController.Instance,
          };
    }

    public override void Enter(State state)
    {
      base.Enter(state);
      animator.CrossFade(jumpStartHash, 0.1f);
    }

    public override void OnAnimFinish(int hash)
    {
      if (hash == jumpStartHash)
      {
        JumpController.Instance.DoJump();
        TransitionTo(stateMachine.jumpAscendState);
      }
    }

  }
}