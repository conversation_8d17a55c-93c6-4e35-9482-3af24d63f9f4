
using System.Collections.Generic;
using UnityEngine;

namespace Sinner
{
  public class JumpDescendState : State
  {

    private readonly int jumpDescendHash = Animator.StringToHash("Jump_Descend");
    private readonly int jumpLandHash = Animator.StringToHash("Jump_Land");

    public JumpDescendState(StateMachine stateMachine) : base(stateMachine)
    {
    }
    public override HashSet<BaseActionController<Brain, StateMachine, State>> InitializeControllers()
    {
      return
          new HashSet<BaseActionController<Brain, StateMachine, State>>
          {
            JumpController.Instance,
          };
    }

    public override void Enter(State state)
    {
      base.Enter(state);
      animator.CrossFade(jumpDescendHash, 0.1f);
    }

    public override void OnAnimFinish(int hash)
    {
      if (hash == jumpLandHash)
      {
        TransitionTo(stateMachine.idleState);
      }
    }

    public override void FrameUpdate()
    {
      if (brain.IsGrounding)
      {
        JumpController.Instance.DoLand();
        animator.Play(jumpLandHash);
      }
    }

  }
}