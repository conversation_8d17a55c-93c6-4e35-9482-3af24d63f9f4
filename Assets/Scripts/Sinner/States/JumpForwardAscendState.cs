
using System.Collections.Generic;
using UnityEngine;

namespace Sinner
{
  public class JumpForwardAscendState : State
  {
    private readonly int jumpForwardAscendHash = Animator.StringToHash("Jump_Forward_Ascend");

    public JumpForwardAscendState(StateMachine stateMachine) : base(stateMachine)
    {
    }
    public override HashSet<BaseActionController<Brain, StateMachine, State>> InitializeControllers()
    {
      return
          new HashSet<BaseActionController<Brain, StateMachine, State>>
          {
            JumpController.Instance,
          };
    }

    public override void Enter(State state)
    {
      base.Enter(state);
      animator.CrossFade(jumpForwardAscendHash, 0.0f);
    }

    public override void FrameUpdate()
    {
      var velocity = brain.ControllerRigidbody.velocity.y;
      if (velocity < 0f)
      {
        stateMachine.TransitionTo(stateMachine.jumpForwardDescendState);
      }
    }

  }
}