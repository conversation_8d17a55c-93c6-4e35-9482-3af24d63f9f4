
using System.Collections.Generic;
using UnityEngine;

namespace Sinner
{
  public class JumpAscendState : State
  {
    private readonly int jumpAscendHash = Animator.StringToHash("Jump_Ascend");

    public JumpAscendState(StateMachine stateMachine) : base(stateMachine)
    {
    }
    public override HashSet<BaseActionController<Brain, StateMachine, State>> InitializeControllers()
    {
      return
          new HashSet<BaseActionController<Brain, StateMachine, State>>
          {
            JumpController.Instance,
          };
    }

    public override void Enter(State state)
    {
      base.Enter(state);
      animator.CrossFade(jumpAscendHash, 0.1f);
    }

    public override void FrameUpdate()
    {
      var velocity = brain.ControllerRigidbody.velocity.y;
      if (velocity < 0f)
      {
        stateMachine.TransitionTo(stateMachine.jumpDescendState);
      }
    }

  }
}