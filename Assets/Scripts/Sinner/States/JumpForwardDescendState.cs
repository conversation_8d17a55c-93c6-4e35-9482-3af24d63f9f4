
using System.Collections.Generic;
using UnityEngine;

namespace Sinner
{
  public class JumpForwardDescendState : State
  {
    private readonly int jumpForwardDescendHash = Animator.StringToHash("Jump_Forward_Descend");
    private readonly int jumpForwardLandHash = Animator.StringToHash("Jump_Forward_Land");

    public JumpForwardDescendState(StateMachine stateMachine) : base(stateMachine)
    {
    }
    public override HashSet<BaseActionController<Brain, StateMachine, State>> InitializeControllers()
    {
      return
          new HashSet<BaseActionController<Brain, StateMachine, State>>
          {
            JumpController.Instance,
          };
    }

    public override void Enter(State state)
    {
      base.Enter(state);
      animator.CrossFade(jumpForwardDescendHash, 0.1f);
    }

    public override void OnAnimFinish(int hash)
    {
      if (hash == jumpForwardLandHash)
      {
        TransitionTo(stateMachine.idleState);
      }
    }

    public override void FrameUpdate()
    {
      if (brain.IsGrounding)
      {
        JumpController.Instance.DoLand();
        animator.Play(jumpForwardLandHash);
      }
    }

  }
}