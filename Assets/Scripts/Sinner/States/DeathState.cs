
using System.Collections.Generic;
using UnityEngine;

namespace Sinner
{
  public class DeathState : State
  {

    private readonly int deathHalfHash = Animator.StringToHash("Death_Half");
    private readonly int deathHalfBackHash = Animator.StringToHash("Death_Half_Back");

    private Dictionary<DamagerType, int[]> deathHashMap = new Dictionary<DamagerType, int[]>();

    public DeathState(StateMachine stateMachine) : base(stateMachine)
    {
      deathHashMap.Add(DamagerType.Half, new int[] { deathHalfHash, deathHalfBackHash });
    }
    public override HashSet<BaseActionController<Brain, StateMachine, State>> InitializeControllers()
    {
      return new HashSet<BaseActionController<Brain, StateMachine, State>>
          {
            // FacingController.Instance,
            // MoveController.Instance,
            // JumpController.Instance,
            // TransmitController.Instance,
            // DodgeController.Instance,
            // JumpController.Instance,
            // AttackComboAController.Instance
          };
    }

    public override void Enter(State prevState)
    {
      base.Enter(prevState);
    }

    public override void OnAnimFinish(int hash)
    {
    }

    public void DieBy(DamagerType damagerType, Vector2 direction)
    {
      bool fromBack = Vector2.Dot(direction, brain.FacingDirection) > 0;
      int hashIndex = fromBack ? 1 : 0;

      if (deathHashMap.ContainsKey(damagerType))
      {
        animator.CrossFade(deathHashMap[damagerType][hashIndex], 0.1f);
      }
    }
  }
}