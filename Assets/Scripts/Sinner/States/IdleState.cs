
using System.Collections.Generic;
using UnityEngine;

namespace Sinner
{
  public class IdleState : State
  {

    private readonly int idleHash = Animator.StringToHash("Idle");

    public IdleState(StateMachine stateMachine) : base(stateMachine)
    {
    }
    public override HashSet<BaseActionController<Brain, StateMachine, State>> InitializeControllers()
    {
      return new HashSet<BaseActionController<Brain, StateMachine, State>>
          {
            FacingController.Instance,
            MoveController.Instance,
            JumpController.Instance,
            // TransmitController.Instance,
            // DodgeController.Instance,
            // JumpController.Instance,
            // AttackComboAController.Instance
          };
    }

    public override void Enter(State prevState)
    {
      base.Enter(prevState);
      if (prevState == null)
      {
        animator.Play(idleHash);
      }
      else
      {
        if (prevState == stateMachine.moveState)
        {
          animator.CrossFade(idleHash, 0.1f);
        }
        else 
        {
          animator.CrossFade(idleHash, 0.1f);
        }
      }

    }

    public override void FrameUpdate()
    {
      if (!brain.IsGrounding)
      {
        stateMachine.TransitionTo(stateMachine.jumpDescendState);
      }
      else if (!Mathf.Approximately(UserInput.Instance.MoveInput.x, 0f))
      {
        stateMachine.TransitionTo(stateMachine.moveState);
      }

    }
  }
}