using System;
using UnityEditor.Experimental;
using UnityEngine;
using UnityEngine.InputSystem;


[DefaultExecutionOrder(-100)]
public class UserInput : MonoBehaviour
{
    public static UserInput Instance;

    public Vector2 MoveInput { private set; get; }
    public bool JumpPressed { private set; get; }
    public bool AttackAPressed { private set; get; }
    public bool AttackBPressed { private set; get; }
    public bool DodgePressed { private set; get; }
    public bool BlockPressed { private set; get; }
    public bool AttackRemotePressed { private set; get; }
    public bool GrabPressed { private set; get; }
    public bool FocusPressed { private set; get; }
    public bool ChangeFocusPressed { private set; get; }
    public bool TestPressed { private set; get; }
    public bool JumpHold { private set; get; }
    public bool AttackAHold { private set; get; }
    public bool AttackBHold { private set; get; }
    public float AttackAHoldTime { private set; get; }
    public float AttackBHoldTime { private set; get; }
    public bool JumpReleasedExact { private set; get; }
    public bool FocusHold { private set; get; }

    private float lastPositiveYPressed = 0;
    private float lastNegativeYPressed = 0;

    [SerializeField] float YPressDuration = 0.2f;


    private PlayerInput _playerInput;

    private InputAction _moveInputAction;
    private InputAction _jumpInputAction;
    private InputAction _testInputAction;
    private InputAction _attackAInputAction;
    private InputAction _attackBInputAction;
    private InputAction _dashInputAction;
    private InputAction _blockInputAction;
    private InputAction _attackRemoteInputAction;
    private InputAction _grabInputAction;
    private InputAction _focusInputAction;
    private InputAction _changeFocusInputAction;

    private bool readyToClear = false;

    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
        }
        _playerInput = GetComponent<PlayerInput>();
        SetupInputActions();
    }

    private void SetupInputActions()
    {
        _moveInputAction = _playerInput.actions["Move"];
        _jumpInputAction = _playerInput.actions["Jump"];
        _testInputAction = _playerInput.actions["Test"];
        _attackAInputAction = _playerInput.actions["AttackA"];
        _attackBInputAction = _playerInput.actions["AttackB"];
        _dashInputAction = _playerInput.actions["Dash"];
        _blockInputAction = _playerInput.actions["Block"];
        _attackRemoteInputAction = _playerInput.actions["AttackRemote"];
        _grabInputAction = _playerInput.actions["Grab"];
        _focusInputAction = _playerInput.actions["Focus"];
        _changeFocusInputAction = _playerInput.actions["ChangeFocus"];
    }

    // Start is called before the first frame update
    void Start()
    {
    }

    // Update is called once per frame
    void Update()
    {
        ClearInputs();

        ProcessInputs();
    }

    void FixedUpdate()
    {
        readyToClear = true;
    }

    void ProcessInputs()
    {
        MoveInput += _moveInputAction.ReadValue<Vector2>();
        MoveInput = new (Mathf.Clamp(MoveInput.x, -1, 1), Mathf.Clamp(MoveInput.y, -1, 1));
        if (MoveInput.y > 0)
        {
            lastPositiveYPressed = Time.time;
        }
        else if (MoveInput.y < 0)
        {
            lastNegativeYPressed = Time.time;
        }

        JumpPressed = JumpPressed || _jumpInputAction.WasPressedThisFrame();
        AttackAPressed = AttackAPressed || _attackAInputAction.WasPressedThisFrame();
        AttackBPressed = AttackBPressed || _attackBInputAction.WasPressedThisFrame();
        DodgePressed = DodgePressed || _dashInputAction.WasPressedThisFrame();
        BlockPressed = BlockPressed || _blockInputAction.WasPressedThisFrame();
        AttackRemotePressed = AttackRemotePressed || _attackRemoteInputAction.WasPressedThisFrame();
        GrabPressed = GrabPressed || _grabInputAction.WasPressedThisFrame();
        FocusPressed = FocusPressed || _focusInputAction.WasPressedThisFrame();
        ChangeFocusPressed = ChangeFocusPressed || _changeFocusInputAction.WasPerformedThisFrame();

        TestPressed = TestPressed || _testInputAction.WasPressedThisFrame();


        JumpHold = _jumpInputAction.IsPressed();
        FocusHold = _focusInputAction.IsPressed();

        if (_attackAInputAction.IsPressed())
        {
            AttackAHold = true;
            AttackAHoldTime += Time.deltaTime;
        }
        else
        {
            AttackAHold = false;
            // if (AttackAHoldTime > 0)
            //     Debug.Log("A Hold Time: " + AttackAHoldTime);
            AttackAHoldTime = 0;
        }

        if (_attackBInputAction.IsPressed())
        {
            AttackBHold = true;
            AttackBHoldTime += Time.deltaTime;
        }
        else
        {
            AttackBHold = false;
            // if (AttackBHoldTime > 0)
            //     Debug.Log("B Hold Time: " + AttackBHoldTime);
            AttackBHoldTime = 0;
        }

        JumpReleasedExact = _jumpInputAction.WasReleasedThisFrame();

    }

    void ClearInputs()
    {
        if (!readyToClear)
            return;

        MoveInput = Vector2.zero;
        JumpPressed = false;
        AttackAPressed = false;
        AttackBPressed = false;
        DodgePressed = false;
        BlockPressed = false;
        AttackRemotePressed = false;
        GrabPressed = false;
        FocusPressed = false;


        TestPressed = false;

        readyToClear = false;
    }

    public bool AttackATriggerred
    {
        get
        {
            return AttackAPressed && !AttackBPressed && !DodgePressed && !AttackCutDownTriggerred && !AttackUpperCutTriggerred && !AttackAirSpinCutTriggerred;
        }
    }

    public bool AttackBTriggerred
    {
        get
        {
            return AttackBPressed && !AttackAPressed && !DodgePressed;
        }
    }

    public bool DodgeTriggerred
    {
        get
        {
            return DodgePressed && !AttackAPressed && !AttackBPressed;
        }
    }

    public bool AttackCutDownTriggerred
    {
        get
        {
            return AttackAPressed && Mathf.Approximately(MoveInput.x, 0f) && MoveInput.y < 0;
        }
    }

    public bool AttackAerialCleaveTriggerred
    {
        get
        {
            return AttackAPressed && MoveInput.y < 0 && Mathf.Approximately(MoveInput.x, 0f);
        }
    }

    public bool AttackAirSpinCutTriggerred
    {
        get
        {
            return AttackAPressed && MoveInput.y < 0 && !Mathf.Approximately(MoveInput.x, 0f);
        }
    }

    public bool AttackUpperCutTriggerred
    {
        get
        {
            return AttackAPressed && Mathf.Approximately(MoveInput.x, 0f) && MoveInput.y > 0 && (Time.time - lastNegativeYPressed > YPressDuration);
        }
    }

    public bool AttackCircleCutTriggerred
    {
        get
        {
            return AttackAPressed && MoveInput.y > 0 && (Time.time - lastNegativeYPressed <= YPressDuration);
        }
    }
    
    public bool AttackAntiAirCutTriggerred
    {
        get
        {
            return AttackAPressed && !Mathf.Approximately(MoveInput.x, 0f) && MoveInput.y > 0 && (Time.time - lastNegativeYPressed > YPressDuration);
        }
    }

    public bool AttackSpinDashTriggerred
    {
        get
        {
            return AttackAPressed && !Mathf.Approximately(MoveInput.x, 0f) && MoveInput.y < 0f;
        }
    }

    public bool AttackDashSlashTriggerred
    {
        get
        {
            return AttackAPressed && DodgePressed;
        }
    }

    public bool AttackUltraSlashTriggerred
    {
        get
        {
            return AttackAPressed && AttackBPressed;
        }
    }

    public bool TailsmanCrosscutTriggerred
    {
        get
        {
            return AttackBPressed && !AttackAPressed && !DodgePressed && !TailsmanCirclecutTriggerred && !TailsmanUpperCutTriggerred;
        }
    }
    public bool TailsmanCirclecutTriggerred
    {
        get
        {
            // return AttackBPressed && MoveInput.y > 0 && (Time.time - lastNegativeYPressed <= YPressDuration);
            return AttackBPressed && MoveInput.y < 0;
        }
    }
    public bool TailsmanUpperCutTriggerred
    {
        get
        {
            return AttackBPressed && MoveInput.y > 0;
        }
    }
    public bool TailsmanDashTriggerred
    {
        get
        {
            return AttackBPressed && DodgePressed;
        }
    }

}
