using UnityEditor;
using UnityEngine;

[CustomPropertyDrawer(typeof(LayerAttribute))]
public class LayerAttributeDrawer : PropertyDrawer
{
    public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
    {
        // 确保属性是 int 类型
        if (property.propertyType == SerializedPropertyType.Integer)
        {
            // 显示标签
            EditorGUI.BeginProperty(position, label, property);

            // 获取当前 Layer 索引
            int layerIndex = property.intValue;

            // 显示 Layer 下拉菜单
            layerIndex = EditorGUI.LayerField(position, label, layerIndex);

            // 更新属性值
            property.intValue = layerIndex;

            EditorGUI.EndProperty();
        }
        else
        {
            // 如果不是 int 类型，显示错误信息
            EditorGUI.LabelField(position, label.text, "Use LayerAttribute with int.");
        }
    }
}

public class LayerAttribute : PropertyAttribute
{
    // 这是一个标记属性，不需要实现任何逻辑
}