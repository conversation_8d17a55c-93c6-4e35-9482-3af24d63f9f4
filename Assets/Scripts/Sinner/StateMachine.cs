
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Sinner
{

  public class StateMachine : CharacterStateMachine<Brain, StateMachine, State>
  {

    public IdleState idleState;
    public MoveState moveState;
    public JumpStartState jumpStartState;
    public JumpAscendState jumpAscendState;
    public JumpDescendState jumpDescendState;
    public JumpForwardAscendState jumpForwardAscendState;
    public JumpForwardDescendState jumpForwardDescendState;
    public DeathState deathState;

    public StateMachine(Brain brain) : base(brain)
    {
      idleState = new IdleState(this);
      moveState = new MoveState(this);
      jumpStartState = new JumpStartState(this);
      jumpAscendState = new JumpAscendState(this);
      jumpDescendState = new JumpDescendState(this);
      jumpForwardAscendState = new JumpForwardAscendState(this);
      jumpForwardDescendState = new JumpForwardDescendState(this);
      deathState = new DeathState(this);
    }

  }

}
