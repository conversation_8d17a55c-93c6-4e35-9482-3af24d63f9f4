using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class PoopHeadController : MonoBehaviour
{
    [SerializeField] float lifeTime = 5f;
    // Start is called before the first frame update
    private float elapsedTime = 0f;

    private int count = 0;

    // Update is called once per frame
    void Update()
    {
        elapsedTime += Time.deltaTime;
        if (elapsedTime > lifeTime)
        {
            elapsedTime = 0f;
            gameObject.SetActive(false);
        }
    }

    public void AddPoop(Vector3 position, Vector3 normal)
    {
        gameObject.SetActive(true);
        float angle = Vector2.SignedAngle(Vector2.up, normal);
        transform.rotation = Quaternion.Euler(0, 0, angle);

        elapsedTime = 0f;
        transform.position = position;
    }
}
