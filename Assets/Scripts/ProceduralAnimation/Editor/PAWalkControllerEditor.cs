using UnityEngine;
using UnityEditor;
using System.Diagnostics;

[CustomEditor(typeof(PAWalkController))]
public class PAWalkControllerEditor : Editor
{
  public override void OnInspectorGUI()
  {
    base.OnInspectorGUI();
    DrawRuntimeButtons();

    if (GUILayout.Button("Test"))
    {
      // 按钮1点击逻辑
      PAWalkController component = (PAWalkController)target;
      component.Test();
    }

  }

  [Conditional("UNITY_EDITOR")]
  private void DrawRuntimeButtons()
  {
    if (!EditorApplication.isPlaying) return;

    EditorGUILayout.BeginHorizontal();
    {
      // 第一个按钮
      if (GUILayout.Button("start"))
      {
        // 按钮1点击逻辑
        PAWalkController component = (PAWalkController)target;
        component.StartWalk();
      }

      // 第二个按钮
      if (GUILayout.Button("stop"))
      {
        // 按钮2点击逻辑
        PAWalkController component = (PAWalkController)target;
        component.StopWalk();
      }
    }
    EditorGUILayout.EndHorizontal();
  }
}
