using UnityEngine;
using UnityEditor;
using System.Diagnostics;

[CustomEditor(typeof(PARunController))]
public class PARunControllerEditor : Editor
{
  public override void OnInspectorGUI()
  {
    base.OnInspectorGUI();
    DrawRuntimeButtons();
  }

  [Conditional("UNITY_EDITOR")]
  private void DrawRuntimeButtons()
  {
    if (!EditorApplication.isPlaying) return;

    EditorGUILayout.BeginHorizontal();
    {
      // 第一个按钮
      if (GUILayout.Button("start"))
      {
        // 按钮1点击逻辑
        PARunController component = (PARunController)target;
        component.StartWalk();
      }

      // 第二个按钮
      if (GUILayout.Button("stop"))
      {
        // 按钮2点击逻辑
        PARunController component = (PARunController)target;
        component.StopWalk();
      }
    }
    EditorGUILayout.EndHorizontal();
  }
}
