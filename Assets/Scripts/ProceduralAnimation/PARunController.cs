
using System;
using System.Collections;
using UnityEngine;

public class PARunController : MonoBehaviour
{
  [SerializeField] PALeg legL;
  [SerializeField] PALeg legR;
  [SerializeField] PAArm armL;
  [SerializeField] PAArm armR;
  [SerializeField] Transform hip;
  [SerializeField] AnimationCurve stepXNCurve;
  [SerializeField] AnimationCurve stepYNCurve;
  [SerializeField] float stepSpacing;
  [SerializeField] float stepHeight;
  [SerializeField] float hipHeight;
  [SerializeField] float hipOffsetX;
  [SerializeField] AnimationCurve hipOffsetYCurve;
  [SerializeField] AnimationCurve hipOffsetYDCurve;

  [SerializeField] AnimationCurve armSwingOffsetYCurve;
  [SerializeField] Transform shoulderTarget;
  [SerializeField] float armLength;
  [SerializeField] float armSpacing;

  [SerializeField] LayerMask groundLayerMask;
  [SerializeField] float stepDurationCoefficient;

  private Vector2 FacingDirection = Vector2.right;

  // hipOffsetX is retain in moving
  float currentHipOffsetX = 0f;

  bool isRunning = false;

  public void StartWalk()
  {
    if (!isRunning)
    {
      isRunning = true;
      MoveOneStep(true);
    }
  }

  public void StopWalk()
  {
    isRunning = false;
  }

  void MoveOneStep(bool isStart = false)
  {
    // get the moving leg
    var backLeg = legL;
    var toL = legL.target.position - legR.target.position;
    if (Vector2.Dot(toL, FacingDirection) > 0)
    {
      backLeg = legR;
    }

    // get the target position of the moving leg
    var frontLeg = backLeg == legL ? legR : legL;
    var movingPosition = hip.position;
    movingPosition.x = stepSpacing + frontLeg.target.position.x;
    // cast the ground
    var hit = Physics2D.Raycast(movingPosition, Vector2.down, 10f, groundLayerMask);
    if (hit.collider == null || isRunning == false)
    {
      if (!Mathf.Approximately(Vector3.Distance(backLeg.target.position, frontLeg.target.position), 0f))
      {
        isRunning = false;
        StartCoroutine(MoveLegToTarget(backLeg, frontLeg, frontLeg.target.position, isStart));
      }
    }
    else
    {
      StartCoroutine(MoveLegToTarget(backLeg, frontLeg, hit.point, isStart));
    }
  }

  IEnumerator MoveLegToTarget(PALeg moveLeg, PALeg standLeg, Vector2 targetPosition, bool isStart)
  {
    var distance = Vector2.Distance(moveLeg.target.position, targetPosition);
    var duration = GetStepDuration(distance);

    float time = 0f;
    var legStartPosition = moveLeg.target.position;

    PAArm backArm = moveLeg == legL ? armL : armR;
    PAArm forwardArm = backArm == armL ? armR : armL;

    var backArmStartPosition = backArm.target.localPosition;
    var forwardStartPosition = forwardArm.target.localPosition;

    Vector2 backArmTargetPosition;
    Vector2 forwardArmTargetPosition;
    if (isRunning)
    {
      backArmTargetPosition = new Vector2(-FacingDirection.x * armSpacing, shoulderTarget.localPosition.y - armLength);
      forwardArmTargetPosition = new Vector2(FacingDirection.x * armSpacing, shoulderTarget.localPosition.y - armLength);
    }
    else
    {
      backArmTargetPosition = new Vector2(0f, shoulderTarget.localPosition.y - armLength);
      forwardArmTargetPosition = new Vector2(0f, shoulderTarget.localPosition.y - armLength);
    }

    while (time < duration)
    {
      var t = time / duration;
      // move the leg
      MoveLeg(t, moveLeg, legStartPosition, targetPosition, out var groundPosition);

      // MoveArm(t, backArm, backArmStartPosition, backArmTargetPosition, isStart);
      // MoveArm(t, forwardArm, forwardStartPosition, forwardArmTargetPosition, isStart);

      // move hip
      // MoveHip(t, moveLeg, standLeg);
      // move self
      transform.position = (groundPosition + (Vector2)standLeg.target.position) / 2f;
      yield return null;
      time += Time.deltaTime;
    }

    moveLeg.target.position = targetPosition;
    backArm.target.localPosition = backArmTargetPosition;
    forwardArm.target.localPosition = forwardArmTargetPosition;
    transform.position = ((Vector2)standLeg.target.position + targetPosition) / 2f;
    MoveOneStep();
  }

  void MoveArm(float t, PAArm arm, Vector2 startPosition, Vector2 endPosition, bool isStart)
  {
    var position = Vector2.Lerp(startPosition, endPosition, t);
    var dt = Mathf.Abs(position.x) / armSpacing;
    var offset  = armSwingOffsetYCurve.Evaluate(dt) * armMaxYOffset;
    position.y += offset;
    arm.target.localPosition = position;
  }

  void MoveLeg(float t, PALeg leg, Vector2 startPosition, Vector2 endPosition, out Vector2 groundPosition)
  {
    groundPosition = Vector2.Lerp(startPosition, endPosition, stepXNCurve.Evaluate(t));
    var position = groundPosition;
    position.y += stepYNCurve.Evaluate(t) * stepHeight;
    leg.target.position = position;
  }

  void MoveHip(float t, PALeg moveLeg, PALeg standLeg)
  {
      // hip
      if (!isRunning)
      {
        // not linear due to start and end not fixed
        currentHipOffsetX = Mathf.Lerp(currentHipOffsetX, 0, t);
      }
      else
      {
        currentHipOffsetX = Mathf.Lerp(currentHipOffsetX, hipOffsetX, t);
      }

      var hipPosition = hip.localPosition;
      hipPosition.x = currentHipOffsetX;

      // t is the distance of standLeg target and hip X,
      float dt = Mathf.Abs(standLeg.target.position.x - hip.position.x) / stepSpacing;
      float hipOffsetY = hipOffsetYDCurve.Evaluate(dt);

      var legDistance = Mathf.Abs(moveLeg.target.position.x - standLeg.target.position.x);
      hipPosition.y = GetHipHeight(legDistance) + hipOffsetY;

      hip.localPosition = hipPosition;

  }

  private float GetStepDuration(float distance)
  {
    return stepDurationCoefficient * Mathf.Sqrt(distance);
  }

  private float GetHipHeight(float distance)
  {
    var half = distance / 2f;
    return Mathf.Sqrt(hipHeight * hipHeight - half * half);
  }

  private float armMaxYOffset
  {
    get => armLength - Mathf.Sqrt(armLength * armLength - armSpacing * armSpacing);
  }

  private Vector2 OffsetY(Vector2 position, float distance = 1f)
  {
    return position + Vector2.up * distance;
  }


}