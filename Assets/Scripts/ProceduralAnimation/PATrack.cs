

using System;
using UnityEngine;

[Serializable]
public struct PATrack {
  public AnimationCurve curve;
  public float offset;

  public bool IsValid {
    get {
      return curve != null;
    }
  }

  public float AnimationUpdate(float t)
  {
    return curve.Evaluate(t + offset);
  }

  public float Duration {
    get { 
      if (curve.length == 0) return 0f;
      
      return curve[curve.length - 1].time;
    }
  }

    public override bool Equals(object other)
    {
      if (other is not PATrack)
      {
        return false;
      }
      return Equals((PATrack)other);
    }

    public bool Equals(PATrack other)
    {
      return curve == other.curve && offset == other.offset;
    }

    public static bool operator ==(PATrack a, PATrack b) {
    return a.Equals(b);
  }

  public static bool operator !=(PATrack a, PATrack b) {
    return !(a == b);
  }

  public override int GetHashCode()
    {
      return curve.GetHashCode() ^ (offset.GetHashCode() << 2);
    }
}