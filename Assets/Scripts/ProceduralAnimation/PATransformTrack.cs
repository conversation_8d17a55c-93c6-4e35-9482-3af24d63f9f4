// #nullable enable

using System;
using UnityEngine;

[Serializable]
public class PATransformTrack
{
  public Transform transform;
  public PATrack positionXTrack;
  public PATrack positionYTrack;
  public PATrack rotationTrack;

  public void AnimationUpdate(float t)
  {
    transform.GetLocalPositionAndRotation(out var position, out var rotation);

    if (positionXTrack.IsValid)
    {
      position.x = positionXTrack.AnimationUpdate(t);
    }
    if (positionYTrack.IsValid)
    {
      position.y = positionYTrack.AnimationUpdate(t);
    }

    if (rotationTrack.IsValid)
    {
      rotation.z = rotationTrack.AnimationUpdate(t);
    }

    transform.localPosition = position;
    transform.localRotation = rotation;
  }
}