using System.Collections.Generic;
using UnityEditor;
using UnityEngine.Profiling;
using UnityEngine.Scripting.APIUpdating;

namespace UnityEngine.U2D.IK
{
  /// <summary>
  /// Component responsible for 2D Forward And Backward Reaching Inverse Kinematics (FABRIK) IK.
  /// </summary>
  // [MovedFrom("UnityEngine.Experimental.U2D.IK")]
  [Solver2DMenu("Chain (FABRIK Rotation)")]
  public sealed class FabrikRotationSolver2D : Solver2D
  {
    const float k_MinTolerance = 0.001f;
    const int k_MinIterations = 1;

    [SerializeField]
    IKChain2D m_Chain = new IKChain2D();

    [SerializeField]
    [Range(k_MinIterations, 50)]
    int m_Iterations = 10;

    [SerializeField]
    [Range(k_MinTolerance, 0.1f)]
    float m_Tolerance = 0.01f;

    [SerializeField]
    Vector2[] m_ConstrainRotations;

    [SerializeField]
    Vector2 m_RootDir = Vector2.down;

    float[] m_Lengths;
    Vector2[] m_Positions;
    Vector3[] m_WorldPositions;


    /// <summary>
    /// Get and set the solver's integration count.
    /// </summary>
    public int iterations
    {
      get => m_Iterations;
      set => m_Iterations = Mathf.Max(value, k_MinIterations);
    }

    /// <summary>
    /// Get and set target distance tolerance.
    /// </summary>
    public float tolerance
    {
      get => m_Tolerance;
      set => m_Tolerance = Mathf.Max(value, k_MinTolerance);
    }

    /// <summary>
    /// Returns the number of chains in the solver.
    /// </summary>
    /// <returns>Returns 1, because FABRIK Solver has only one chain.</returns>
    protected override int GetChainCount() => 1;

    /// <summary>
    /// Gets the chain in the solver at index.
    /// </summary>
    /// <param name="index">Index to query. Not used in this override.</param>
    /// <returns>Returns IKChain2D for the Solver.</returns>
    public override IKChain2D GetChain(int index) => m_Chain;

    protected override void DoInitialize()
    {
      if (m_Chain.effector == null || m_Chain.transformCount <= 1)
        return;
      // get default dir of root
      int num = m_Chain.transformCount;
      Transform parent = m_Chain.effector;
      while ((bool)parent && num >= 3)
      {
        parent = parent.parent;
        num--;
      }

      m_RootDir = (parent.position - parent.parent.position).normalized;
    }

    /// <summary>
    /// Prepares the data required for updating the solver.
    /// </summary>
    protected override void DoPrepare()
    {
      if (m_Positions == null || m_Positions.Length != m_Chain.transformCount)
      {
        m_Positions = new Vector2[m_Chain.transformCount];
        m_Lengths = new float[m_Chain.transformCount - 1];
        m_WorldPositions = new Vector3[m_Chain.transformCount];
      }

      for (var i = 0; i < m_Chain.transformCount; ++i)
      {
        m_Positions[i] = GetPointOnSolverPlane(m_Chain.transforms[i].position);
      }

      for (var i = 0; i < m_Chain.transformCount - 1; ++i)
      {
        m_Lengths[i] = (m_Positions[i + 1] - m_Positions[i]).magnitude;
      }
    }

    /// <summary>
    /// Updates the IK and sets the chain's transform positions.
    /// </summary>
    /// <param name="targetPositions">Target position for the chain.</param>
    protected override void DoUpdateIK(List<Vector3> targetPositions)
    {
      // Profiler.BeginSample(nameof(FabrikSolver2D.DoUpdateIK));

      var targetPosition = targetPositions[0];
      targetPosition = GetPointOnSolverPlane(targetPosition);
      var rootDir = m_Chain.rootTransform.InverseTransformDirection(m_RootDir);
      if (FABRIKRotation2D.Solve(targetPosition, iterations, tolerance, m_Lengths, rootDir, m_ConstrainRotations, ref m_Positions))
      {
        // Convert all plane positions to world positions
        for (var i = 0; i < m_Positions.Length; ++i)
        {
          m_WorldPositions[i] = GetWorldPositionFromSolverPlanePoint(m_Positions[i]);
        }

        for (var i = 0; i < m_Chain.transformCount - 1; ++i)
        {
          var startLocalPosition = (Vector2)m_Chain.transforms[i + 1].localPosition;
          var endLocalPosition = (Vector2)m_Chain.transforms[i].InverseTransformPoint(m_WorldPositions[i + 1]);
          m_Chain.transforms[i].localRotation *= Quaternion.AngleAxis(Vector2.SignedAngle(startLocalPosition, endLocalPosition), Vector3.forward);
        }
      }

      // Profiler.EndSample();
    }

    void OnDrawGizmos()
    {
      var chain = GetChain(0);

      if (chain.transformCount < 1)
        return;

      Handles.color = new Color(1, 0, 0, 0.1f);
      for (int i = 0; i < chain.transforms.Length - 1; i++)
      {
        var dir = m_RootDir;
        if (i > 0)
        {
          dir = (chain.transforms[i].position - chain.transforms[i - 1].position).normalized;
        }
        dir = Quaternion.AngleAxis(m_ConstrainRotations[i].x, Vector3.forward) * dir;
        Handles.DrawSolidArc(chain.transforms[i].position, Vector3.forward, dir, m_ConstrainRotations[i].y - m_ConstrainRotations[i].x, 1f);
      }
    }

  }
}