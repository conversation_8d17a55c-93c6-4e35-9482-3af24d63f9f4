using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.InputSystem;

public class RopeVerlet : MonoBehaviour
{
    [Header("Rope")]
    [SerializeField] int _numOfRopeSegments = 50;
    [SerializeField] float _ropeSegmentLength = 0.225f; 
    [Header("Physics")]
    [SerializeField] Vector2 _gravityForce = new Vector2(0, -10f);
    [SerializeField] float _dampingFactor = 0.98f;
    [SerializeField] LayerMask _collisionMask;
    [SerializeField] float _collisionRadius = 0.1f;
    [SerializeField] float _bounceFactor = 0.1f;
    [SerializeField] float _correctionClampAmount = 0.1f;



    [Header("Constraints")]
    [SerializeField] int _numOfConstraintRuns = 50;

    [Header("Optimizations")]
    [SerializeField] int _collisionSegmentInterval = 2;


    private List<RopeSegment> _ropeSegments = new List<RopeSegment>();
    private LineRenderer _lineRenderer;
    private Vector3 _ropeStart;

    void Awake()
    {
        _lineRenderer = GetComponent<LineRenderer>();
        _lineRenderer.positionCount = _numOfRopeSegments;

        var mousePosition = Mouse.current.position.ReadValue();
        _ropeStart = Camera.main.ScreenToWorldPoint(new Vector3(mousePosition.x, mousePosition.y, 10));
        _ropeStart.z = 1;
        for (int i = 0; i < _numOfRopeSegments; i++)
        {
            _ropeSegments.Add(new RopeSegment(_ropeStart));
            _ropeStart.y -= _ropeSegmentLength;
        }
    }
    
    void Update()
    {
        // var xx = Physics2D.OverlapCircleAll(new Vector2(0, 0), 0.1f, _collisionMask);
        // Debug.Log($"Overlap: " + xx.Length);
        // if (xx.Length > 0)
        // {
        //     Debug.Log(xx[0].name);
        // }
        DrawRope();
    }

    void FixedUpdate()
    {
        Simulate();

        // for (int i = 0; i < _numOfConstraintRuns; i++)
        // {
        //     ApplyConstraints();
        //     if (i % _collisionSegmentInterval == 0)
        //     {
        //         HandleCollisions();
        //     }
        // }

        // for (int i = 0; i < _numOfConstraintRuns; i++)
        // {
        //     ApplyConstraints();
        //     if (i % _collisionSegmentInterval == 0)
        //     {
        //         HandleCollisions();
        //     }
        // }
    }

    void DrawRope()
    {
        Vector3[] ropePositions = new Vector3[_numOfRopeSegments];
        for (int i = 0; i < _ropeSegments.Count; i++)
        {
            ropePositions[i] = _ropeSegments[i].CurrentPosition;
        }
        _lineRenderer.SetPositions(ropePositions);
    }

    private void Simulate()
    {
        for (int i = 0; i < _ropeSegments.Count; i++)
        {
            var segment = _ropeSegments[i];
            Vector2 velocity = (segment.CurrentPosition - segment.OldPosition) * _dampingFactor;
            segment.OldPosition = segment.CurrentPosition;
            segment.CurrentPosition += velocity;
            segment.CurrentPosition += _gravityForce * Time.fixedDeltaTime;

            _ropeSegments[i] = segment;
        }
    }

    private void ApplyConstraints()
    {
        var firstRopeSegment = _ropeSegments[0];
        var mousePosition = Mouse.current.position.ReadValue();
        var position = Camera.main.ScreenToWorldPoint(new Vector3(mousePosition.x, mousePosition.y, 10));

        firstRopeSegment.CurrentPosition = position;
        _ropeSegments[0] = firstRopeSegment;

        for (int i = 0; i < _numOfRopeSegments - 1; i++)
        {
            var currSeg = _ropeSegments[i];
            var nextSeg = _ropeSegments[i + 1];

            float dist = (currSeg.CurrentPosition - nextSeg.CurrentPosition).magnitude;
            float difference = dist - _ropeSegmentLength;

            var changeDir = (currSeg.CurrentPosition - nextSeg.CurrentPosition).normalized;
            var changeVector = changeDir * difference;

            if (i != 0)
            {
                currSeg.CurrentPosition -= changeVector * 0.5f;
                nextSeg.CurrentPosition += changeVector * 0.5f;
            }
            else
            {
                nextSeg.CurrentPosition += changeVector;
            }

            _ropeSegments[i] = currSeg;
            _ropeSegments[i + 1] = nextSeg;
        }
        
    }

    private void HandleCollisions()
    {
        for (int i = 1; i < _ropeSegments.Count; i++)
        {
            var segment = _ropeSegments[i];
            Vector2 velocity = segment.CurrentPosition - segment.OldPosition;
            Collider2D[] colliders = Physics2D.OverlapCircleAll(segment.CurrentPosition, _collisionRadius, _collisionMask);
            foreach (var collider in colliders)
            {
                Vector2 closestPoint = collider.ClosestPoint(segment.CurrentPosition);
                float distance = Vector2.Distance(closestPoint, segment.CurrentPosition);

                if (distance < _collisionRadius)
                {
                    Vector2 normal = (segment.CurrentPosition - closestPoint).normalized;
                    if (normal == Vector2.zero)
                    {
                        normal = (segment.CurrentPosition - (Vector2)collider.transform.position).normalized;
                    }

                    float depth = _collisionRadius - distance;
                    segment.CurrentPosition += normal * depth;

                    velocity = Vector2.Reflect(velocity, normal) * _bounceFactor;

                }
                else
                {
                    Debug.Log($"distance {distance}");
                }
            }
            segment.OldPosition = segment.CurrentPosition - velocity;
            _ropeSegments[i] = segment;
        }
    }

    

    public struct RopeSegment
    {
        public Vector2 CurrentPosition;
        public Vector2 OldPosition;

        public RopeSegment(Vector2 pos)
        {
            CurrentPosition = pos;
            OldPosition = pos;
        }
    }



}
