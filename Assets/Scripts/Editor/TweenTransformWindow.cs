
using System.Linq;
using UnityEditor;
using UnityEngine;

public class TweenTransformWindow : EditorWindow
{
  // private float currentTime = 0f;

  private static float leftPanelWidth = 200f;
  private const float minLeftPanelWidth = 150f;
  private const float maxLeftPanelWidth = 300f;
  private Rect resizeHandleRect;
  private int resizeHandleControlID;

  private float duration;
  private float scale;
  private Vector2 scaleRange = new(1f / 60f, 60f * 60 * 10 / 30f);

  private Vector2 perScaleRange;
  private float currentPerScale;
  private float minScaleLength;

  private Vector2 scrollPosition;
  private float currentPointLength;

  private void OnEnable()
  {
    Debug.Log("Enable");
    duration = 1f;
    scale = 10f / 30f;
    minScaleLength = 30f;

    scrollPosition = Vector2.zero;
  }

  // 创建纯色纹理的辅助函数
  private Texture2D MakeColor(Color col)
  {
    Color[] pix = new Color[1];
    pix[0] = col;
    Texture2D result = new Texture2D(1, 1);
    result.SetPixels(pix);
    result.Apply();
    return result;
  }

  private Texture2D MakeGradient(Color from, Color to, float stop)
  {
    Color[] colors = new Color[32];
    for (int i = 0; i < colors.Length; i++)
    {
      colors[i] = Color.Lerp(from, to, i / (colors.Length - 1f) / stop);
    }
    colors = colors.Reverse().ToArray();
    var tex = new Texture2D(1, 32);
    tex.wrapMode = TextureWrapMode.Clamp;

    tex.SetPixels(colors);
    tex.Apply();
    return tex;
  }

  [MenuItem("Window/Tween Transform")]
  public static void ShowWindow()
  {
    GetWindow<TweenTransformWindow>("Tween Transform");
  }

  void OnGUI()
  {
    EditorGUILayout.BeginHorizontal();

    // 左边面板
    DrawLeftPanel();

    // 可调整的分割线
    DrawResizeHandle();

    // 右边面板
    DrawRightPanel();

    EditorGUILayout.EndHorizontal();

    // 处理鼠标事件
    HandleMouseEvents();


    // DrawTimeline();
  }

  private void DrawLeftPanel()
  {
    // 限制左边面板的宽度在范围内
    leftPanelWidth = Mathf.Clamp(leftPanelWidth, minLeftPanelWidth, maxLeftPanelWidth);

    EditorGUILayout.BeginVertical(GUILayout.Width(leftPanelWidth));

    // 左边面板内容
    GUILayout.Label("Left Panel", EditorStyles.boldLabel);
    EditorGUILayout.HelpBox("This is the left panel with restricted width.", MessageType.Info);
    // EditorGUILayout.Space();
    EditorGUILayout.LabelField("Width: " + leftPanelWidth);

    EditorGUILayout.EndVertical();
  }

  private void DrawResizeHandle()
  {
    var style = GetStyle(new Color(0.0f, 0.0f, 0.0f, 0.4f));
    // 绘制可拖动的分割线
    resizeHandleControlID = GUIUtility.GetControlID(FocusType.Passive);
    GUILayout.Box("", style, GUILayout.Width(2f), GUILayout.ExpandHeight(true));
    resizeHandleRect = GUILayoutUtility.GetLastRect();
    // EditorGUI.DrawRect(resizeHandleRect, new Color(0.0f, 0.0f, 0.0f, .2f));
  }

  private GUIStyle GetStyle(Color color)
  {
    GUIStyle customStyle = new GUIStyle()
    {
      normal = new GUIStyleState()
      {
        background = MakeColor(color), // 绿色背景
        // textColor = Color.black // 黑色文字
      },
      padding = new RectOffset(0, 0, 0, 0),
      margin = new RectOffset(0, 0, 0, 0),
      contentOffset = new Vector2(0, 0),
      alignment = TextAnchor.MiddleCenter
    };
    return customStyle;
  }

  private void DrawRightPanel()
  {
    EditorGUILayout.BeginVertical(GUILayout.ExpandWidth(true));
    DrawTimeline();
    Rect layoutRect = GUILayoutUtility.GetLastRect();

    Handles.DrawLine(new Vector2(layoutRect.x + 40f, 0f), new Vector2(layoutRect.x + 40f, currentPointLength));

    EditorGUILayout.EndVertical();
    if (Event.current.type == EventType.Repaint)
    {
      var rect = GUILayoutUtility.GetLastRect();
      currentPointLength = rect.height;
    }
  }
  private void HandleMouseEvents()
  {
    Event evt = Event.current;

    switch (evt.GetTypeForControl(resizeHandleControlID))
    {
      case EventType.MouseDown:
        if (resizeHandleRect.Contains(evt.mousePosition))
        {
          GUIUtility.hotControl = resizeHandleControlID;
          evt.Use();
        }
        break;
      case EventType.MouseDrag:
        if (GUIUtility.hotControl == resizeHandleControlID)
        {
          leftPanelWidth += evt.delta.x;
          leftPanelWidth = Mathf.Clamp(leftPanelWidth, minLeftPanelWidth, maxLeftPanelWidth);
          evt.Use();
        }
        break;
      case EventType.MouseUp:
        if (GUIUtility.hotControl == resizeHandleControlID)
        {
          GUIUtility.hotControl = 0;
          evt.Use();
        }
        break;
      case EventType.Repaint:
        if (GUIUtility.hotControl == resizeHandleControlID || GUIUtility.hotControl == 0)
          EditorGUIUtility.AddCursorRect(resizeHandleRect, MouseCursor.ResizeHorizontal, resizeHandleControlID);
        break;
    }
  }

  private void DrawTimeline()
  {
    Rect backgroundRect = GUILayoutUtility.GetRect(position.width, 20f);
    EditorGUI.DrawRect(backgroundRect, new Color(0.2f, 0.2f, 0.2f));

    var offset = 20f;
    Rect rect = new Rect(backgroundRect.x + offset, backgroundRect.y, backgroundRect.width - offset, backgroundRect.height);
    EditorGUI.DrawRect(rect, new Color(0.24f, 0.24f, 0.24f));

    Rect margin = GUILayoutUtility.GetRect(position.width, 20f);
    GUI.DrawTexture(margin, MakeGradient(new Color(0.1f, 0.1f, 0.1f), new Color(0.19f, 0.19f, 0.19f), .2f));



    // int i = 0;
    // var x = 0f;
    // while (x < rect.xMax)
    // {
    //   x = rect.xMin + i * currentPerScale;
    //   Handles.DrawLine(new Vector3(x, rect.yMax), new Vector3(x, rect.yMax - (i % 2 == 0 ? 10 : 5)));
    //   ++i;
    // }

    EditorGUILayout.BeginScrollView(scrollPosition, false, false, GUILayout.ExpandHeight(true), GUILayout.ExpandWidth(true));

    EditorGUILayout.EndScrollView();


    // var bodyRect = GUILayoutUtility.GetRect(position.width, position.height);
    // EditorGUI.DrawRect(bodyRect, new Color(0.1f, 0.1f, 0.1f));


    // for (float time = 0; time <= duration; )

    // Handles.color = Color.gray;
    // Handles.BeginGUI();
    // Handles.DrawLine(new Vector3(0, 0), new Vector3(10, 10));
    // Handles.EndGUI();

    // // 绘制刻度
    // Handles.BeginGUI();
    // Handles.color = Color.gray;

    // for (float time = 0; time <= duration; time += 1.0f)
    // {
    //   float x = Mathf.Lerp(timelineRect.xMin, timelineRect.xMax, time / duration);
    //   Handles.DrawLine(new Vector3(x, timelineRect.yMin), new Vector3(x, timelineRect.yMax));
    // }

    // // 绘制当前时间指针
    // float pointerX = Mathf.Lerp(timelineRect.xMin, timelineRect.xMax, currentTime / duration);
    // Handles.DrawLine(new Vector3(pointerX, timelineRect.yMin), new Vector3(pointerX, timelineRect.yMax), 2);
    // Handles.EndGUI();

    // // 处理时间轴上的点击
    // if (Event.current.type == EventType.MouseDown && timelineRect.Contains(Event.current.mousePosition))
    // {
    //   float normalizedTime = (Event.current.mousePosition.x - timelineRect.xMin) / timelineRect.width;
    //   currentTime = Mathf.Clamp(normalizedTime * duration, 0, duration);
    //   Event.current.Use();
    // }
  }
}