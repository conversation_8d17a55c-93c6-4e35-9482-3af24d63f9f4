using UnityEditor;
using UnityEngine;

[CustomEditor(typeof(SnakeController))]
public class SnakeControllerEditor : Editor
{
    private SnakeController snakeController;
    void OnEnable()
    {
        snakeController = (SnakeController)target;
    }

    void OnSceneGUI()
    {
        ChangePosition(snakeController.root);
        ChangePosition(snakeController.head);

        ChangeMouthOpenRange();
    }

    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();
    }

    void ChangeMouthOpenRange()
    {
        EditorGUI.BeginChangeCheck();
        Vector3 newPosition = Handles.FreeMoveHandle(
            snakeController.root.position + Vector3.right * snakeController.mouthOpenRange.x,
            0.1f,
            Vector3.zero,
            Handles.DotHandleCap
        );

        if (EditorGUI.EndChangeCheck())
        {
            Undo.RecordObject(snakeController, "CHange Mouth Range X");
            snakeController.mouthOpenRange.x = Vector2.Distance(newPosition, snakeController.root.position);
        }

        EditorGUI.BeginChangeCheck();
        newPosition = Handles.FreeMoveHandle(
            snakeController.root.position + Vector3.right * snakeController.mouthOpenRange.y,
            0.1f,
            Vector3.zero,
            Handles.DotHandleCap
        );

        if (EditorGUI.EndChangeCheck())
        {
            Undo.RecordObject(snakeController, "CHange Mouth Range Y");
            snakeController.mouthOpenRange.y = Vector2.Distance(newPosition, snakeController.root.position);
        }

    }

    void ChangePosition(Transform transform)
    {
        if (transform != null)
        {
            // 使用Handles创建可拖拽的位置控制点
            EditorGUI.BeginChangeCheck();
            // Vector3 newPosition = Handles.PositionHandle(gizmo.transform.position, Quaternion.identity);

            Vector3 newPosition = Handles.FreeMoveHandle(
                transform.position,
                1f, Vector3.zero, Handles.CircleHandleCap
            );

            if (EditorGUI.EndChangeCheck())
            {
                Undo.RecordObject(transform, "Move Target Object");

                // 更新目标对象位置
                transform.position = newPosition;
            }

        }

    }
}