using UnityEditor;
using UnityEngine;

[CustomEditor(typeof(ThreeBones))]
public class ThreeBonesEditor : Editor
{
    void OnSceneGUI()
    {
        ThreeBones threeBone = (ThreeBones)target;
        
        if (threeBone.target == null) return;

        // 使用Handles创建可拖拽的位置控制点
        EditorGUI.BeginChangeCheck();
        // Vector3 newPosition = Handles.PositionHandle(gizmo.transform.position, Quaternion.identity);

        Vector3 newPosition = Handles.FreeMoveHandle(
            threeBone.target.position,
            1f, Vector3.zero,Handles.SphereHandleCap
        );
        
        if (EditorGUI.EndChangeCheck())
        {
            Undo.RecordObject(threeBone.target, "Move Target Object");
            Undo.RecordObject(threeBone.transform, "Move Gizmo");
            
            // 更新目标对象位置
            threeBone.target.position = newPosition;
        }
    }

    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();
        
        if (GUILayout.But<PERSON>("Reset"))
        {
            ((ThreeBones)target).SetRotation();
        }
    }
}