
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

[System.Serializable]
public struct BoneChain
{
  public Transform effector;
  public int chainLength;
}

public class BoneTransformInfo
{
  public Transform transform;
  public Vector3 localPosition;
  public Vector3 localRotation;
}

public class ChainMoveConstrain
{
  private Transform _root;
  private BoneChain _boneChain;
  BonePoint[] bonePoints;
  float[] lengths;
  Vector3[] directions;

  public ChainMoveConstrain(Transform root, BoneChain bonechain)
  {
    _root = root;
    _boneChain = bonechain;
    Initialize();
  }

  void Initialize()
  {
    // move the chain to position
    var chainLength = _boneChain.chainLength;
    bonePoints = new BonePoint[chainLength];
    lengths = new float[chainLength - 1];
    directions = new Vector3[chainLength - 1];
    var tr = _boneChain.effector;
    for (int i = 0; i < chainLength; i++)
    {
      bonePoints[i] = new BonePoint() {
        transform = tr,
        defaultPosition = tr.position
      };
      tr = tr.parent;
    }

    for (int i = 0; i < chainLength - 1; ++i)
    {
      directions[i] = bonePoints[i].defaultPosition - bonePoints[i + 1].defaultPosition;
      lengths[i] = Vector3.Distance(bonePoints[i].defaultPosition, bonePoints[i + 1].defaultPosition);
    }
  }

  public (AnimData from, AnimData to) MoveTo(Vector3 position)
  {
    var chainLength = _boneChain.chainLength;
    var positions = new Vector3[chainLength];
    positions[0] = position;
    for (int j = 1; j < chainLength; j++)
    {
      positions[j] = positions[j - 1] + (bonePoints[j].defaultPosition - positions[j - 1]).normalized * lengths[j - 1];
    }
    // Debug.Log(string.Join(", ", positions));

    // last position
    // bone rotation;
    List<AnimData.Binding> frombindings = new();
    List<AnimData.Binding> tobindings = new();

    var last = chainLength - 1;
    var localPosition = bonePoints[last].transform.parent ? bonePoints[last].transform.parent.InverseTransformPoint(positions[last]) : positions[last];
    frombindings.AddRange(AnimHelper.MakeAnimPositionBinding(_root, bonePoints[last].transform, bonePoints[last].transform.localPosition));
    tobindings.AddRange(AnimHelper.MakeAnimPositionBinding(_root, bonePoints[last].transform, localPosition));

    // rotate bones
    int i = last;
    int rotationIndex = 0;
    Quaternion accQuaternion = Quaternion.identity;
    while(i > 0)
    {
      var currentDir = positions[i - 1] - positions[i];

      var tDir = accQuaternion * directions[i - 1];
      var localRotation = bonePoints[i].transform.localRotation;
      var rotation = localRotation * Quaternion.FromToRotation(tDir, currentDir);
      frombindings.AddRange(AnimHelper.MakeAnimRotationBinding(_root, bonePoints[i].transform, localRotation.eulerAngles));
      tobindings.AddRange(AnimHelper.MakeAnimRotationBinding(_root, bonePoints[i].transform, rotation.GetClosestEquivalentQuaternion(localRotation)));

      accQuaternion *= Quaternion.FromToRotation(directions[i - 1], currentDir);

      rotationIndex++;
      i--;
    }

    // Debug.Log(rotations[0].eulerAngles);
    // Debug.Log(string.Join(", ", rotations.Select(r => r.eulerAngles)));
    var fromAnimData = ScriptableObject.CreateInstance<AnimData>();
    fromAnimData.bindings = frombindings.ToArray();

    var toAnimData = ScriptableObject.CreateInstance<AnimData>();
    toAnimData.bindings = tobindings.ToArray();

    // return (new() { bindings = frombindings.ToArray() }, new() { bindings = tobindings.ToArray() });
    return (fromAnimData, toAnimData);
  }
}
