using UnityEngine;
using System;


public class AnimationEventManager<B, M, S> where B : CharacterBrain<B, M, S> where M : CharacterStateMachine<B, M, S> where S : CharacterState<B, M, S>
{
  public event Action<int> completed;
  private CharacterBrain<B, M, S> characterController;

  private bool isAnimationPlaying = false;

  public AnimationEventManager(CharacterBrain<B, M, S> characterController)
  {
    this.characterController = characterController;
  }

  public void FrameUpdate()
  {
    if (characterController.Animator.runtimeAnimatorController == null)
      return;

    var stateInfo = characterController.Animator.GetCurrentAnimatorStateInfo(0);

    if (stateInfo.normalizedTime < 1.0f)
    {
      isAnimationPlaying = true;
    } else if (isAnimationPlaying)
    {
      isAnimationPlaying = false;
      completed?.Invoke(stateInfo.shortNameHash);
    }
  }
}