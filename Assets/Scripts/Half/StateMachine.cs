
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Half
{

  public class StateMachine : CharacterStateMachine<Brain, StateMachine, State>
  {

    public IdleState idleState;
    public ShowHideState showHideState;
    public MadState madState;

    public StateMachine(Brain brain) : base(brain)
    {
      idleState = new IdleState(this);
      showHideState = new ShowHideState(this);
      madState = new MadState(this);
    }

  }

}
