
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Half
{
  public class ShowHideState : State
  {
    private readonly int showHash = Animator.StringToHash("Show");
    private readonly int hideHash = Animator.StringToHash("Hide");
    private ShowHideController showHideController;
    public ShowHideState(StateMachine stateMachine) : base(stateMachine)
    {
      showHideController = brain.GetComponent<ShowHideController>();
    }

    public override HashSet<BaseActionController<Brain, StateMachine, State>> InitializeControllers()
    {
      return new HashSet<BaseActionController<Brain, StateMachine, State>>
          {
            showHideController
          };
    }

    public override void Enter(State prevState)
    {
      base.Enter(prevState);

      animator.CrossFade(showHash, 0.1f);
    }

    public override void OnAnimFinish(int hash)
    {
      if (hash == showHash)
      {
        showHideController.DoHide();
      }
      else if (hash == hideHash)
      {
        showHideController.DoShow();
      }
    }

    public void Show(float speed)
    {
      animator.speed = speed;
      animator.Play(showHash);
    }

    public void Hide(float speed)
    {
      animator.speed = speed;
      animator.Play(hideHash);
    }

    public override void Exit()
    {
      base.Exit();
      animator.speed = 1f;
      showHideController.Exit();
    }

  }
}