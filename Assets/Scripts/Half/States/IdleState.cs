
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Half
{
  public class IdleState : State
  {
    private readonly int idleHash = Animator.StringToHash("Idle");
    // private IdleController idleController;
    public IdleState(StateMachine stateMachine) : base(stateMachine)
    {
      // idleController = brain.GetComponent<IdleController>();
    }

    public override HashSet<BaseActionController<Brain, StateMachine, State>> InitializeControllers()
    {
      return new HashSet<BaseActionController<Brain, StateMachine, State>>
          {
            // idleController,
          };
    }

    public override void Enter(State prevState)
    {
      base.Enter(prevState);

      if (prevState == null)
      {
        animator.Play(idleHash);
      }
      else
      {
        animator.CrossFade(idleHash, 0.1f);
      }
    }
  }
}