
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Half
{
  public class MadState : State
  {
    private readonly int madStartHash = Animator.StringToHash("Mad_Start");
    private readonly int madMoveHash = Animator.StringToHash("Mad_Move");
    private readonly int hairMoveHash = Animator.StringToHash("Hair_Move");
    private MadController madController;
    public MadState(StateMachine stateMachine) : base(stateMachine)
    {
      madController = brain.GetComponent<MadController>();
    }

    public override HashSet<BaseActionController<Brain, StateMachine, State>> InitializeControllers()
    {
      return new HashSet<BaseActionController<Brain, StateMachine, State>>
          {
            madController
          };
    }

    public override void Enter(State prevState)
    {
      base.Enter(prevState);

      animator.CrossFade(madStartHash, 0.1f);
    }

    public override void OnAnimFinish(int hash)
    {
      if (hash == madStartHash)
      {
        animator.Play(madMoveHash);
        animator.CrossFade(hairMoveHash, 0.1f, 1);
        madController.GoMad();
      }
    }

    public override void Exit()
    {
      base.Exit();
      madController.Exit();
    }

  }
}