

using System.Collections;
using UnityEngine;

namespace Half
{
  public class MadController : ActionController
  {
    [SerializeField] float moveSpeed = 15;
    [SerializeField] float maxMoveDistance = 40f;

    private Vector2 startPosition;
    private bool hasMad = false;

    public void GoMad()
    {
      startPosition = controllerRigidbody.position;
      controllerRigidbody.velocity = brain.FacingDirection * moveSpeed;
      hasMad = true;
    }

    public void FixedUpdate()
    {
      if (!hasMad) return;

      float distance = Vector2.Distance(controllerRigidbody.position, startPosition);
      if (distance > maxMoveDistance)
      {
        brain.FlipDirection();
        startPosition = controllerRigidbody.position;
        controllerRigidbody.velocity = brain.FacingDirection * moveSpeed;
      }
    }


    public void Exit()
    {
      hasMad = false;
      StopAllCoroutines();
    }

  }

}