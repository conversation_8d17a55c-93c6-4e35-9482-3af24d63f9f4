

using System.Collections;
using UnityEngine;

namespace Half
{
  public class ShowHideController : ActionController
  {
    [SerializeField] Vector2 durationRange = new (0f, 6f);
    [SerializeField] Vector2 animSpeedRange = new (0.5f, 2f);

    public void DoShow()
    {
      StartCoroutine(ShowCoroutine());
    }

    public void DoHide()
    {
      StartCoroutine(HideCoroutine());
    }

    public IEnumerator ShowCoroutine()
    {
      yield return new WaitForSeconds(Random.Range(durationRange.x, durationRange.y));
      stateMachine.showHideState.Show(Random.Range(animSpeedRange.x, animSpeedRange.y));
    }

    public IEnumerator HideCoroutine()
    {
      yield return new WaitForSeconds(Random.Range(durationRange.x, durationRange.y));
      stateMachine.showHideState.Hide(Random.Range(animSpeedRange.x, animSpeedRange.y));
    }

    public void Exit()
    {
      StopAllCoroutines();
    }

  }

}