
using UnityEngine.Splines;
using UnityEngine;

namespace Half
{
  public class Brain : AttackBrain<Brain, StateMachine, State>, IDisturbable
  {
    [HideInInspector] public Vector2 Origin { private set; get; }
    [SerializeField] Transform EyeTransform;
    [SerializeField] float eyesight;
    [SerializeField] LayerMask characterLayerMask;
    // public bool eyeOpen { private set; get; } = false;
    private bool eyeOpen = false;
    public override DamagerType DamageType => DamagerType.Half;

    void Start()
    {
      StateMachine = new StateMachine(this);
      StateMachine.Initialize(StateMachine.idleState);

      Origin = transform.position;
    }

    protected override void Update()
    {
      base.Update();

      if (eyeOpen)
      {
        var hit = Physics2D.Raycast(EyeTransform.position, FacingDirection, eyesight, characterLayerMask);
        if (hit.collider != null)
        {

          hit.collider.TryGetComponent<Rigidbody2D>(out var rb);
          if (rb != null)
          {
            if (rb.velocity.magnitude > 0f)
            {
              StateMachine.TransitionTo(StateMachine.madState);
            }
          }
        }
      }
    }

    void OnDrawGizmos()
    {
      var startPosition = EyeTransform.transform.position;
      var direction = transform.localScale.x < 0 ? Vector2.left : Vector2.right;
      var endPosition = startPosition + (Vector3)direction * eyesight;
      GizmosExt.DrawLine(startPosition, endPosition, Color.yellow);
    }

    public void DisturbBy(IDisturber disturber, int level)
    {
      if (StateMachine.CurrentState == StateMachine.idleState)
      {
        StateMachine.TransitionTo(StateMachine.showHideState);
      }
    }

    public void LoseDisturb(int level)
    {
    }

    void OpenEye(int open)
    {
      eyeOpen = open != 0;
    }

  }

  public enum FlyPosition
  {
    InAir,
    InGround,
  }
  
}