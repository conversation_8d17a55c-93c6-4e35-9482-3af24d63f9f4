using UnityEditor;
using UnityEngine;

[CustomEditor(typeof(SnakeController2))]
public class SnakeController2Editor : Editor
{
    private SnakeController2 controller;
    void OnEnable()
    {
      controller = (SnakeController2)target;
    }

    void OnSceneGUI()
    {
        controller.root.position = Handles.PositionHandle(controller.root.position, Quaternion.identity);

        // Visualize the tangent lines
        // Handles.DrawDottedLine(be.startPoint, be.startTangent, 5);
        // Handles.DrawDottedLine(be.endPoint, be.endTangent, 5);

        // Handles.DrawBezier(controller.root.position, controller.neck.position, controller.root.right * controller.rootTangent, controller.neck.right * controller.neckTangent, Color.red, null, 5);
    }

    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();
    }
    void ChangePosition(ref Vector3 position)
    {
        // 使用Handles创建可拖拽的位置控制点
        EditorGUI.BeginChangeCheck();
        // Vector3 newPosition = Handles.PositionHandle(gizmo.transform.position, Quaternion.identity);

        Vector3 newPosition = Handles.FreeMoveHandle(
            position,
            1f, Vector3.zero, Handles.SphereHandleCap
        );

        if (EditorGUI.EndChangeCheck())
        {
            Undo.RecordObject(controller, "Move Target Object");

            // 更新目标对象位置
            position = newPosition;
        }


    }
}