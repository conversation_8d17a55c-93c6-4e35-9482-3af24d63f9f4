using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Sparrow;
using UnityEngine;
using UnityEngine.Events;

public abstract class DisturbArea : MonoBehaviour
{
    [SerializeField] GameObject disturbObject;
    [SerializeField] float lossDisturbTime = 5;
    [SerializeField] int level = 0;

    private IDisturbable disturbable;
    float currentTime = 0;
    bool disturbLoss = true;

    HashSet<IDisturber> characters = new();

    protected virtual void Awake()
    {
        disturbable = disturbObject.GetComponent<IDisturbable>();
    }

    protected virtual void Update()
    {
        if (characters.Count == 0)
        {
            currentTime += Time.deltaTime;
            if (currentTime > lossDisturbTime && !disturbLoss)
            {
                disturbLoss = true;
                disturbable.LoseDisturb(level);
            }
        }
    }

    protected virtual void OnTriggerEnter2D(Collider2D other)
    {
        other.TryGetComponent<IDisturber>(out var disturber);
        if (disturber == null)
            return;

        characters.Add(disturber);
        currentTime = 0;
        disturbLoss = false;
        
        if (characters.Count == 1)
        {
            disturbable.DisturbBy(disturber, level);
        }
    }

    protected virtual void OnTriggerExit2D(Collider2D other)
    {
        other.TryGetComponent<IDisturber>(out var disturber);
        if (disturber == null)
            return;

        characters.Remove(disturber);
    }
}
