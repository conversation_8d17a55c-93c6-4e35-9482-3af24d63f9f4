using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Sparrow;
using UnityEngine;
using UnityEngine.Events;

[RequireComponent(typeof(CircleCollider2D))]
public class DisturbCircleArea : DisturbArea
{
    [SerializeField] float radius = 1f;

    protected override void Awake()
    {
        base.Awake();
        var collider = GetComponent<CircleCollider2D>();
        collider.radius = radius;
    }

}
