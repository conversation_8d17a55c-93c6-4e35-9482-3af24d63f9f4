using UnityEngine;

 
public class BendableTreeBranch : MonoBehaviour
{
  [SerializeField] Transform root;
  [SerializeField] float maxBendAngle = 10f;
  [Range(0f, 10f)]
  [SerializeField] float bendness = 0f;
  [Range(0f, 10f)]
  [SerializeField] float forceLength = 0f;
  [Range(0f, 10f)]
  [SerializeField] float force = 0f;

  private Quaternion initialRotation;

  void Start()
  {
    initialRotation = root.rotation;
  }

  void Update()
  {
    var angle = CalculateBendAngle();
    root.rotation = initialRotation * Quaternion.AngleAxis(angle, -Vector3.forward);
  }

  float CalculateBendAngle()
  {
    var bendAngle = forceLength * force * bendness;
    return Mathf.Max(0f, Mathf.Min(bendAngle, maxBendAngle));
  }
}