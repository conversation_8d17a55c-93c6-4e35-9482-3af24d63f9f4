// using System;
// using System.Linq;
// using System.Linq.Expressions;
// using System.Reflection;
// using UnityEngine;

// public class SnakeBoneController : MonoBehaviour
// {
//   class Feature
//   {
//     public float angle;
//     public float lengthOffset;
//     public float amplitude;
//   }

//   public Transform root;
//   public Transform head;
//   public Transform target;
//   [Header("Idle Tween Settings")]
//   [SerializeField] Vector2 angleRange = new(45f, 135f);
//   [SerializeField] float lengthOffsetMultiplier = 1f;
//   [SerializeField] AnimationCurve lengthCurve;
//   [SerializeField] float amplitudeMultiplier = 1f;
//   [SerializeField] AnimationCurve amplitudeCurve;

//   [SerializeField] float idleDuration = 2f;
//   [Header("Aim Tween Settings")]
//   [SerializeField] float aimDuration = .5f;
//   [SerializeField] float aimAmplitude = 1.2f;
//   [SerializeField] float aimAngle = 135f;
//   [SerializeField] float aimLengthOffset = -1f;

//   private LineRenderer lineRenderer;
//   private float targetLength;

//   private TweenAnim<Feature> _tweenAnim;

//   void OnDrawGizmos()
//   {
//     Gizmos.color = Color.green;
//     Gizmos.DrawSphere(root.position, 0.2f);
//     Gizmos.color = Color.yellow;
//     Gizmos.DrawSphere(head.position, 0.2f);
//   }

//   void Start()
//   {
//     targetLength = Vector2.Distance(root.position, head.position);
//     lengthOffsetMultiplier = Mathf.Max(0f, Mathf.Min(lengthOffsetMultiplier, targetLength - 2f));

//     lineRenderer = GetComponent<LineRenderer>();

//     _tweenAnim = gameObject.TweenAnim<Feature>();
//     _tweenAnim.Data.valueChanged += UpdatePoints;
//     // _tweenAnim.Data.valueChanged += TestChange;
//     _tweenAnim.SetLoop(-1, true);

//     _tweenAnim.SetTimeTrack(t => t.angle, Tween.CreateTimeTrackChange(idleDuration, new [] { angleRange.x, angleRange.y }));
//     _tweenAnim.SetTimeTrack(t => t.lengthOffset, Tween.CreateTimeTrackChange(idleDuration, new [] { 0f, lengthOffsetMultiplier }, curve: lengthCurve));
//     _tweenAnim.SetTimeTrack(t => t.amplitude, Tween.CreateTimeTrackChange(idleDuration, new [] { 0f, amplitudeMultiplier }, curve: amplitudeCurve));

//     _tweenAnim.Start();
//   }


//   void TestChange(Feature feature)
//   {
//     Debug.Log(JsonUtility.ToJson(feature));
//   }

//   void Update()
//   {
//     if (Input.GetKeyDown(KeyCode.Space))
//     {
//       _tweenAnim.Rewind();
//       _tweenAnim.SetLoop(1);

//       // _tweenAnim.Data.valueChanged += TestChange;

//       // _tweenAnim.SetTimeTrack(t => t.angle, Tween.CreateTimeTrackChange(aimDuration, aimAngle));
//       _tweenAnim.SetSpeedTrack(t => t.angle, Tween.CreateSpeedTrackChange(
//         () => {
//           return Vector2.SignedAngle(Vector2.right, target.position - root.position);
//         },
//         () => 10,
//         continuous: true
//       ));
//       _tweenAnim.SetTimeTrack(t => t.lengthOffset, Tween.CreateTimeTrackChange(aimDuration, aimLengthOffset));
//       _tweenAnim.SetTimeTrack(t => t.amplitude, Tween.CreateTimeTrackChange(aimDuration, aimAmplitude));
//     }
//   }

//   void UpdatePoints(Feature feature)
//   {
//     var dir = MathfExt.AngleToDirection(feature.angle);
//     var length = targetLength + feature.lengthOffset;

//     head.position = root.position + dir * length;

//     // do
//     int pointCount = GetPointCount(length);
//     Vector3[] points = new Vector3[pointCount];

//     for (int i = 0; i < pointCount; i++)
//     {
//       // points[i] = root.position + dir * i * length / (pointCount - 1);
//       var x = i * length / (pointCount - 1);
//       var y = Mathf.Sin(x * Mathf.PI / length) * feature.amplitude;
//       points[i] = new Vector2(x, y);
//     }

//     Quaternion rotation = Quaternion.FromToRotation(Vector3.right, dir);
//     for (int i = 0; i < pointCount; i++)
//     {
//       points[i] = root.position + rotation * points[i];
//     }


//     lineRenderer.positionCount = pointCount;
//     lineRenderer.SetPositions(points);
//   }

//   int GetPointCount(float length)
//   {
//     return Mathf.CeilToInt(length * 1.5f);
//   }
// }
