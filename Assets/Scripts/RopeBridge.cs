
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.InputSystem;

public class RopeBridge : MonoBehaviour
{
  [Header("Rope")]
  [SerializeField] Transform _anchor_Start;
  [SerializeField] Transform _anchor_End;
  [SerializeField] float _ropeLengthMultiplier = 1.2f;
  [SerializeField] float _ropeSegmentLength = 0.225f;
  [Header("Physics")]
  [SerializeField] Vector2 _gravityForce = new Vector2(0, -2f);
  [SerializeField] float _forceRadius = 2f;
  [SerializeField] float _ropeCollisionRadius = 0.1f;
  [SerializeField] float _dampingFactor = 0.98f;
  [SerializeField] LayerMask _collisionMask;
  [SerializeField] float _collisionRadius = 0.1f;
  [SerializeField] float _bounceFactor = 0.1f;
  [SerializeField] float _correctionClampAmount = 0.1f;

  [Header("Constraints")]
  [SerializeField] int _numOfConstraintRuns = 50;

  [Header("Optimizations")]
  [SerializeField] int _collisionSegmentInterval = 2;


  private List<RopeSegment> _ropeSegments = new List<RopeSegment>();
  private LineRenderer _lineRenderer;
  private EdgeCollider2D _edgeCollider;
  private int _numOfRopeSegments;

  private List<float> distances = new();

  void Awake()
  {
    var distance = Vector2.Distance(_anchor_Start.position, _anchor_End.position);
    _numOfRopeSegments = Mathf.CeilToInt(distance * _ropeLengthMultiplier / _ropeSegmentLength);

    _lineRenderer = GetComponent<LineRenderer>();
    _lineRenderer.positionCount = _numOfRopeSegments + 1;

    var point = _anchor_Start.position;
    var difference = _anchor_End.position - _anchor_Start.position;

    Debug.Log($"length of segment: {(difference * 1 / _numOfRopeSegments).magnitude}");

    for (int i = 0; i <= _numOfRopeSegments; i++)
    {
      _ropeSegments.Add(new RopeSegment(point + difference * i / _numOfRopeSegments));
    }

    _edgeCollider = GetComponent<EdgeCollider2D>();
  }

  void Update()
  {
    DrawRope();
  }

  void FixedUpdate()
  {
    Simulate();

    for (int i = 0; i < _numOfConstraintRuns; i++)
    {
      ApplyConstraints();
      // if (i % _collisionSegmentInterval == 0)
      // {
      //   HandleCollisions();
      // }
    }

    distances.Clear();
    for (int i = 0; i < _ropeSegments.Count - 1; i++)
    {
      distances.Add(Vector2.Distance(_ropeSegments[i + 1].CurrentPosition, _ropeSegments[i].CurrentPosition));
    }
    // Debug.Log($"{distances.Average()}");
  }

  void DrawRope()
  {
    Vector3[] ropePositions = new Vector3[_numOfRopeSegments + 1];
    for (int i = 0; i < _ropeSegments.Count; i++)
    {
      ropePositions[i] = _ropeSegments[i].CurrentPosition;
    }
    _lineRenderer.SetPositions(ropePositions);
    _edgeCollider.points = ropePositions.Select(x => new Vector2(x.x, x.y)).ToArray();
  }

  private void Simulate()
  {
    for (int i = 0; i < _ropeSegments.Count; i++)
    {
      var segment = _ropeSegments[i];
      var force = GetForceOfSegment(segment);

      Vector2 velocity = (segment.CurrentPosition - segment.OldPosition) * _dampingFactor;
      segment.OldPosition = segment.CurrentPosition;
      segment.CurrentPosition += velocity;
      segment.CurrentPosition += force * Time.fixedDeltaTime;

      _ropeSegments[i] = segment;
    }
  }

  private Vector2 GetForceOfSegment(RopeSegment segment)
  {
    var hit = Physics2D.Raycast(segment.CurrentPosition, Vector2.up, _ropeCollisionRadius, _collisionMask);
    if (hit.collider != null)
    {
      var rb = hit.collider.GetComponent<Rigidbody2D>();
      var force = rb.gravityScale * rb.mass * Vector2.down;
      return force + _gravityForce;
    }
    return _gravityForce;
  }

  private void ApplyConstraints()
  {
    var firstRopeSegment = _ropeSegments[0];
    firstRopeSegment.CurrentPosition = _anchor_Start.position;
    _ropeSegments[0] = firstRopeSegment;

    var lastRopeSegment = _ropeSegments[_ropeSegments.Count - 1];
    lastRopeSegment.CurrentPosition = _anchor_End.position;
    _ropeSegments[^1] = lastRopeSegment;

    for (int i = 0; i < _ropeSegments.Count - 1; i++)
    {
      var currSeg = _ropeSegments[i];
      var nextSeg = _ropeSegments[i + 1];

      float dist = (currSeg.CurrentPosition - nextSeg.CurrentPosition).magnitude;
      float difference = dist - _ropeSegmentLength;

      var changeDir = (currSeg.CurrentPosition - nextSeg.CurrentPosition).normalized;
      var changeVector = changeDir * difference;

      if (i == 0)
      {
        nextSeg.CurrentPosition += changeVector;
      }
      else if (i + 1 == _ropeSegments.Count - 1)
      {
        currSeg.CurrentPosition -= changeVector;
      }
      else
      {
        currSeg.CurrentPosition -= changeVector * 0.5f;
        nextSeg.CurrentPosition += changeVector * 0.5f;
      }

      _ropeSegments[i] = currSeg;
      _ropeSegments[i + 1] = nextSeg;
    }

  }

  private void HandleCollisions()
  {
    for (int i = 1; i < _ropeSegments.Count; i++)
    {
      var segment = _ropeSegments[i];
      Vector2 velocity = segment.CurrentPosition - segment.OldPosition;
      Collider2D[] colliders = Physics2D.OverlapCircleAll(segment.CurrentPosition, _collisionRadius, _collisionMask);
      foreach (var collider in colliders)
      {
        Vector2 closestPoint = collider.ClosestPoint(segment.CurrentPosition);
        float distance = Vector2.Distance(closestPoint, segment.CurrentPosition);

        if (distance < _collisionRadius)
        {
          Vector2 normal = (segment.CurrentPosition - closestPoint).normalized;
          if (normal == Vector2.zero)
          {
            normal = (segment.CurrentPosition - (Vector2)collider.transform.position).normalized;
          }

          float depth = _collisionRadius - distance;
          segment.CurrentPosition += normal * depth;

          velocity = Vector2.Reflect(velocity, normal) * _bounceFactor;

        }
        else
        {
          Debug.Log($"distance {distance}");
        }
      }
      segment.OldPosition = segment.CurrentPosition - velocity;
      _ropeSegments[i] = segment;
    }
  }



  public struct RopeSegment
  {
    public Vector2 CurrentPosition;
    public Vector2 OldPosition;

    public RopeSegment(Vector2 pos)
    {
      CurrentPosition = pos;
      OldPosition = pos;
    }
  }



}
