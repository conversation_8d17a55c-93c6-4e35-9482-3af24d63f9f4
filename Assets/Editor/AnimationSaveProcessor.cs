
using UnityEditor;
using UnityEngine;

// public class AnimationClipToAnimData : AssetPostprocessor
// {
//     private void OnPostprocessAnimation(GameObject root, AnimationClip clip)
//     {
//         // 当动画导入或重新导入时调用
//         // GenerateAdditionalInfo(clip);
//         Debug.Log(root);
//         Debug.Log(clip);
//     }

//     private static void GenerateAdditionalInfo(AnimationClip clip)
//     {
//         // 在这里生成你的额外信息
//         Debug.Log($"Processing animation clip: {clip.name}");

//         // 示例：获取动画长度
//         float length = clip.length;

//         // 可以在这里保存额外信息到ScriptableObject或其他地方
//     }
// }


public class AnimationSaveProcessor : AssetModificationProcessor
{
  private static string[] OnWillSaveAssets(string[] paths)
  {
    foreach (string path in paths)
    {
      if (path.EndsWith(".anim"))
      {
        AnimationClip clip = AssetDatabase.LoadAssetAtPath<AnimationClip>(path);
        if (clip != null)
        {
          AnimationTool.GenerateAnimData(clip);
        }
      }
    }
    return paths;
  }
}