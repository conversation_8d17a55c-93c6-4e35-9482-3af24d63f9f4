#if UNITY_EDITOR
using UnityEditor;
using UnityEngine;

public class ComponentMenus : Editor
{
  [MenuItem("CONTEXT/Component/Copy Matching Fields")]
  public static void CopyMatchingFields(MenuCommand command)
  {
    Component source = command.context as Component;
    if (source == null) return;

    EditorGUIUtility.systemCopyBuffer = JsonUtility.ToJson(source);
  }

  [MenuItem("CONTEXT/Component/Paste Matching Fields")]
  public static void PasteMatchingFields(MenuCommand command)
  {
    Component destination = command.context as Component;
    if (destination == null || string.IsNullOrEmpty(EditorGUIUtility.systemCopyBuffer)) return;

    JsonUtility.FromJsonOverwrite(EditorGUIUtility.systemCopyBuffer, destination);
  }
}
#endif

