using UnityEditor;
using UnityEngine;

[InitializeOnLoad]
public class AnimationClipTest : ScriptableObject
{
    static AnimationClipTest()
    {
        var clip = AssetDatabase.LoadAssetAtPath<AnimationClip>("Assets/Animation/GroundSnake/GroundSnake.anim");

        if (clip != null)
        {
            ParseAnimationClip(clip);
        }
    }

    public static void ParseAnimationClip(AnimationClip clip)
    { 
        var bindings = AnimationUtility.GetCurveBindings(clip);
        foreach (EditorCurveBinding binding in bindings)
        {
            // Debug.Log($"Path: {binding.path}, Type: {binding.type.Name}, Property: {binding.propertyName}");
            // var curve = AnimationUtility.GetEditorCurve(clip, binding);
            // Debug.Log(curve[curve.length - 1].time);
        }
    }

}