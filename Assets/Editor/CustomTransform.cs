using UnityEditor;
using UnityEngine;

[CustomEditor(typeof(Transform))]
public class TransformGlobalEditor : Editor
{
    private SerializedProperty position;
    private SerializedProperty rotation;
    private SerializedProperty scale;
    
    private void OnEnable()
    {
        position = serializedObject.FindProperty("m_LocalPosition");
        rotation = serializedObject.FindProperty("m_LocalRotation");
        scale = serializedObject.FindProperty("m_LocalScale");
    }
    
    public override void OnInspectorGUI()
    {
        // Draw the default local transform fields
        EditorGUILayout.PropertyField(position);
        EditorGUILayout.PropertyField(rotation);
        EditorGUILayout.PropertyField(scale);
        
        // Add space before global values
        EditorGUILayout.Space();
        
        // Get the transform component
        Transform transform = (Transform)target;
        
        // Display global position
        EditorGUILayout.LabelField("Global Position", transform.position.ToString());
        
        // Display global rotation (euler angles)
        EditorGUILayout.LabelField("Global Rotation", transform.eulerAngles.ToString());
        
        // Apply any changes made to the local transform fields
        serializedObject.ApplyModifiedProperties();
    }
}