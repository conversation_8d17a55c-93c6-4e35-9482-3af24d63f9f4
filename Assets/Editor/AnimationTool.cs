
#if UNITY_EDITOR

using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

public class AnimationTool : Editor
{
    public static AnimationClip GetSelectedClip()
    {
        var clips = Selection.GetFiltered<AnimationClip>(SelectionMode.Assets);
        return clips.Length > 0 ? clips[0] : null;
    }

    [MenuItem("Tools/Generate AnimData")]
    public static void GenerateAnimData()
    {
        var clip = GetSelectedClip();
        if (clip == null)
            return;
        GenerateAnimData(clip);
    }

    public static void GenerateAnimData(AnimationClip clip)
    {
        var path = AssetDatabase.GetAssetPath(clip);
        path = path.Substring(0, path.LastIndexOf('/'));
        var assetPath = $"{path}/{clip.name}.asset";

        bool exists = true;
        var animData = AssetDatabase.LoadAssetAtPath<AnimData>(assetPath);
        if (animData == null)
        {
            exists = false;
            animData = CreateInstance<AnimData>();
        }

        var bindings = AnimationUtility.GetCurveBindings(clip);
        animData.bindings = new AnimData.Binding[bindings.Length];

        for (int i = 0; i < bindings.Length; i++)
        {
            var curve = AnimationUtility.GetEditorCurve(clip, bindings[i]);
            animData.bindings[i] = new AnimData.Binding
            {
                path = bindings[i].path,
                propertyName = bindings[i].propertyName,
                typeName = bindings[i].type.Name,
                curve = curve
            };
        }

        if (exists)
        {
            EditorUtility.SetDirty(animData);
            AssetDatabase.SaveAssetIfDirty(animData);
        }
        else
        {
            AssetDatabase.CreateAsset(animData, assetPath);
        }

        AssetDatabase.Refresh();
    }

    [MenuItem("Tools/Reverse Animation")]
    public static void Reverse()
    {
        var clip = GetSelectedClip();
        if (clip == null)
            return;
        float clipLength = clip.length;

        List<AnimationCurve> curves = new List<AnimationCurve>();
        EditorCurveBinding[] editorCurveBindings = AnimationUtility.GetCurveBindings(clip);
        foreach (EditorCurveBinding i in editorCurveBindings)
        {
            var curve = AnimationUtility.GetEditorCurve(clip, i);
            curves.Add(curve);
        }

        clip.ClearCurves();
        for (int i = 0; i < curves.Count; i++)
        {
            var curve = curves[i];
            var binding = editorCurveBindings[i];

            var keys = curve.keys;
            int keyCount = keys.Length;
            var postWrapmode = curve.postWrapMode;
            curve.postWrapMode = curve.preWrapMode;
            curve.preWrapMode = postWrapmode;
            for (int j = 0; j < keyCount; j++)
            {
                Keyframe K = keys[j];
                K.time = clipLength - K.time;
                var tmp = -K.inTangent;
                K.inTangent = -K.outTangent;
                K.outTangent = tmp;
                keys[j] = K;
            }
            curve.keys = keys;
            clip.SetCurve(binding.path, binding.type, binding.propertyName, curve);
        }

        var events = AnimationUtility.GetAnimationEvents(clip);
        if (events.Length > 0)
        {
            for (int i = 0; i < events.Length; i++)
            {
                events[i].time = clipLength - events[i].time;
            }
            AnimationUtility.SetAnimationEvents(clip, events);
        }
        Debug.Log("Animation reversed!");
    }
}

#endif