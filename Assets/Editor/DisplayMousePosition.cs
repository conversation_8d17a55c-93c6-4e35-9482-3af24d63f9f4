using UnityEditor;
using UnityEngine;

[InitializeOnLoad]
public class MouseWorldPositionIndicator : ScriptableObject
{
    static MouseWorldPositionIndicator()
    {
        SceneView.duringSceneGui += OnSceneGUI;
    }

    private static void OnSceneGUI(SceneView sceneView)
    {
        // 获取鼠标在Scene视图中的位置
        Vector2 mousePosition = Event.current.mousePosition;
        
        // 将鼠标位置转换为世界坐标
        Ray ray = HandleUtility.GUIPointToWorldRay(mousePosition);
        Vector3 worldPosition = ray.origin;
        
        // 在Scene视图中显示位置信息
        Handles.BeginGUI();
        GUIStyle style = new GUIStyle();
        style.normal.textColor = Color.yellow;
        style.fontSize = 12;
        GUI.Label(new Rect(10, sceneView.position.height - 100, 300, 20), 
                 $"World Position: {worldPosition.ToString("F2")}", style);
        Handles.EndGUI();
        
        // 重绘Scene视图以确保信息更新
        sceneView.Repaint();
    }
}