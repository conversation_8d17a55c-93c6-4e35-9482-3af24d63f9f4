fileFormatVersion: 2
guid: 8698c701782cb41eea82a030f32a5373
importerOverride:
  nativeImporterType: 2089858483
  scriptedImporterType:
    serializedVersion: 2
    Hash: b10b55a1aa6a1aa32521bc0cab59632e
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: b2a9591990af98743ba3ff7cf1000886, type: 3}
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 1
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 1
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 512
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 0
    wrapV: 0
    wrapW: 0
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  spriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
    spritePosition: {x: 0, y: 0}
  mosaicSpriteImportData: []
  rigSpriteImportData:
  - name: "\u56FE\u5C42 3"
    originalName: 
    pivot: {x: 0.5, y: 0.5}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 4
      y: 567
      width: 115
      height: 1262
    spriteID: 9c9be303873114096ad6299b41a3c6cc
    spriteBone:
    - name: bone_3
      guid: 57cc3d75f8968418498ab9c94dbda525
      position: {x: 401.8162, y: 0.000045765508, z: 0}
      rotation: {x: 0, y: 0, z: -0.008195959, w: 0.99996644}
      length: 358.9517
      parentId: 1
      color:
        serializedVersion: 2
        rgba: 4278255360
    - name: bone_2
      guid: 63ce17ec04c5b4b9e8a4a657a4e6021b
      position: {x: 401.8165, y: 0.000091439986, z: 0}
      rotation: {x: 0, y: 0, z: 0.016391074, w: 0.9998657}
      length: 401.8164
      parentId: 2
      color:
        serializedVersion: 2
        rgba: 4278255615
    - name: bone_1
      guid: 591937a913c4f4cdaba1fffb32153f8b
      position: {x: 62.293213, y: 1194.201, z: 0}
      rotation: {x: 0, y: 0, z: -0.71287847, w: 0.7012877}
      length: 401.8164
      parentId: -1
      color:
        serializedVersion: 2
        rgba: 4278190335
    spriteOutline: []
    vertices:
    - position: {x: 115, y: 1}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 115, y: 122.021}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 115, y: 243.04097}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 115, y: 364.063}
      boneWeight:
        weight0: 0.7099727
        weight1: 0.2900273
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 1
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 115, y: 485.083}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 115, y: 606.102}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 115, y: 727.122}
      boneWeight:
        weight0: 0.98678577
        weight1: 0.01321423
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 114.999, y: 848.141}
      boneWeight:
        weight0: 0.94905084
        weight1: 0.050949156
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 1
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 114.999, y: 969.161}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 114.993, y: 1090.183}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 114.933, y: 1211.203}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 114.775, y: 1262}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0, y: 1261.755}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0, y: 1064.734}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0, y: 943.713}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0, y: 822.694}
      boneWeight:
        weight0: 0.7644342
        weight1: 0.23556578
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 1
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0, y: 701.674}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0, y: 580.654}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0.0009994507, y: 459.635}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0.0009994507, y: 338.614}
      boneWeight:
        weight0: 0.9515131
        weight1: 0.048486866
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 1
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0.00799942, y: 217.59297}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0.07500076, y: 96.573}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    - position: {x: 0.3180008, y: 0.68499756}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 2
        boneIndex2: 2
        boneIndex3: 2
    spritePhysicsOutline: []
    indices: 000000001500000001000000000000001600000015000000010000001400000002000000010000001500000014000000020000001300000003000000020000001400000013000000030000001200000004000000030000001300000012000000040000001100000005000000040000001200000011000000050000001000000006000000050000001100000010000000060000000f0000000700000006000000100000000f000000070000000e00000008000000070000000f0000000e000000080000000d00000009000000080000000e0000000d000000090000000d0000000a0000000a0000000c0000000b0000000a0000000d0000000c000000
    edges:
    - {x: 0, y: 1}
    - {x: 0, y: 22}
    - {x: 1, y: 2}
    - {x: 2, y: 3}
    - {x: 3, y: 4}
    - {x: 4, y: 5}
    - {x: 5, y: 6}
    - {x: 6, y: 7}
    - {x: 7, y: 8}
    - {x: 8, y: 9}
    - {x: 9, y: 10}
    - {x: 10, y: 11}
    - {x: 11, y: 12}
    - {x: 12, y: 13}
    - {x: 13, y: 14}
    - {x: 14, y: 15}
    - {x: 15, y: 16}
    - {x: 16, y: 17}
    - {x: 17, y: 18}
    - {x: 18, y: 19}
    - {x: 19, y: 20}
    - {x: 20, y: 21}
    - {x: 21, y: 22}
    tessellationDetail: 0
    uvTransform: {x: 4, y: 567}
    spritePosition: {x: 965, y: 141}
  - name: "\u56FE\u5C42 2"
    originalName: 
    pivot: {x: 0.5, y: 0.5}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 330
      height: 555
    spriteID: 22aee846b5c82429fae78a3d57251df1
    spriteBone:
    - name: hip
      guid: 0d83dded5bc3c40b9a98e4e74b15c979
      position: {x: 110.118286, y: 123.35498, z: 0}
      rotation: {x: 0, y: 0, z: -0.70710677, w: -0.7071068}
      length: 69.28136
      parentId: -1
      color:
        serializedVersion: 2
        rgba: 4294967040
    - name: chest
      guid: 8022cacf02eee42238a3b97c80e1fb1b
      position: {x: 69.28137, y: 0.0000040910454, z: 0}
      rotation: {x: 0, y: 0, z: 0.0072977683, w: 0.99997336}
      length: 306.2114
      parentId: 0
      color:
        serializedVersion: 2
        rgba: 4294901760
    spriteOutline: []
    vertices:
    - position: {x: 111, y: 20}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 135.556, y: 29.556}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 166.613, y: 64.616}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 189.898, y: 120.896}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 202.644, y: 178.35199}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 210.736, y: 236.965}
      boneWeight:
        weight0: 0.9675709
        weight1: 0.03242908
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 212.47, y: 297.67}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 208.601, y: 357.911}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 199.528, y: 416.639}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 183.754, y: 473.223}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 156.718, y: 524.283}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 128.1, y: 551.16003}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 102.619, y: 555}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 79.516, y: 548.515}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 57.362, y: 526.033}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 34.349, y: 488.735}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 16.307999, y: 432.395}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 4.473007, y: 374.601}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 0.44599912, y: 314.595}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 0.2899933, y: 252.962}
      boneWeight:
        weight0: 0.97826636
        weight1: 0.021733638
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 5.4329987, y: 194.373}
      boneWeight:
        weight0: 0.50462204
        weight1: 0.49537796
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 1
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 17.186996, y: 135.629}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 38.253, y: 79.015}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 60.394, y: 44.606995}
      boneWeight:
        weight0: 0
        weight1: 1
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 84.373, y: 24.973007}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 107.43, y: 19.681}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    spritePhysicsOutline: []
    indices: 000000001800000001000000000000001900000018000000010000001700000002000000010000001800000017000000020000001600000003000000020000001700000016000000030000001500000004000000030000001600000015000000040000001400000005000000040000001500000014000000050000001300000006000000050000001400000013000000060000001200000007000000060000001300000012000000070000001100000008000000070000001200000011000000080000001000000009000000080000001100000010000000090000000f0000000a00000009000000100000000f0000000a0000000c0000000b0000000a0000000e0000000c0000000a0000000f0000000e0000000c0000000e0000000d000000
    edges:
    - {x: 0, y: 1}
    - {x: 0, y: 25}
    - {x: 1, y: 2}
    - {x: 2, y: 3}
    - {x: 3, y: 4}
    - {x: 4, y: 5}
    - {x: 5, y: 6}
    - {x: 6, y: 7}
    - {x: 7, y: 8}
    - {x: 8, y: 9}
    - {x: 9, y: 10}
    - {x: 10, y: 11}
    - {x: 11, y: 12}
    - {x: 12, y: 13}
    - {x: 13, y: 14}
    - {x: 14, y: 15}
    - {x: 15, y: 16}
    - {x: 16, y: 17}
    - {x: 17, y: 18}
    - {x: 18, y: 19}
    - {x: 19, y: 20}
    - {x: 20, y: 21}
    - {x: 21, y: 22}
    - {x: 22, y: 23}
    - {x: 23, y: 24}
    - {x: 24, y: 25}
    tessellationDetail: 0
    uvTransform: {x: -910, y: -816}
    spritePosition: {x: 914, y: 1283}
  characterData:
    bones:
    - name: bone_1
      guid: 591937a913c4f4cdaba1fffb32153f8b
      position: {x: -71.15387, y: -3.1749692, z: 0}
      rotation: {x: 0, y: 0, z: 0.9999665, w: 0.008195876}
      length: 401.8163
      parentId: 3
      color:
        serializedVersion: 2
        rgba: 4278190335
    - name: bone_2
      guid: 63ce17ec04c5b4b9e8a4a657a4e6021b
      position: {x: 401.81635, y: 0.000007849766, z: 0}
      rotation: {x: 0, y: 0, z: 0.016391095, w: 0.99986565}
      length: 401.8163
      parentId: 0
      color:
        serializedVersion: 2
        rgba: 4278255615
    - name: bone_3
      guid: 57cc3d75f8968418498ab9c94dbda525
      position: {x: 401.8163, y: -0.00007850634, z: 0}
      rotation: {x: 0, y: 0, z: -0.008195981, w: 0.99996644}
      length: 358.9516
      parentId: 1
      color:
        serializedVersion: 2
        rgba: 4278255360
    - name: hip
      guid: 0d83dded5bc3c40b9a98e4e74b15c979
      position: {x: 1024.1183, y: 1406.355, z: 0}
      rotation: {x: 0, y: 0, z: -0.70710677, w: -0.7071068}
      length: 69.28136
      parentId: -1
      color:
        serializedVersion: 2
        rgba: 4294967040
    - name: chest
      guid: 8022cacf02eee42238a3b97c80e1fb1b
      position: {x: 69.28137, y: -0.000039144976, z: 0}
      rotation: {x: 0, y: 0, z: 0.00729774, w: 0.9999734}
      length: 306.2114
      parentId: 3
      color:
        serializedVersion: 2
        rgba: 4294901760
    parts:
    - spritePosition:
        x: 965
        y: 141
        width: 115
        height: 1262
      spriteId: 9c9be303873114096ad6299b41a3c6cc
      bones: 020000000100000000000000
      parentGroup: 0
      order: 0
    - spritePosition:
        x: 914
        y: 1283
        width: 330
        height: 555
      spriteId: 22aee846b5c82429fae78a3d57251df1
      bones: 0300000004000000
      parentGroup: 0
      order: 0
    dimension: {x: 0, y: 0}
    characterGroups: []
    pivot: {x: 0.5, y: 0}
    boneReadOnly: 0
  sharedRigSpriteImportData: []
  sharedRigCharacterData:
    bones: []
    parts: []
    dimension: {x: 0, y: 0}
    characterGroups: []
    pivot: {x: 0, y: 0}
    boneReadOnly: 0
  platformSettings:
  - name: DefaultTexturePlatform
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  - name: Standalone
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  - name: WebGL
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  mosaicLayers: 1
  characterMode: 1
  documentPivot: {x: 0.5, y: 0}
  documentAlignment: 7
  importHiddenLayers: 0
  layerMappingOption: 2
  generatePhysicsShape: 0
  paperDollMode: 0
  keepDupilcateSpriteName: 1
  skeletonAssetReferenceID: 
  pipeline: {instanceID: 0}
  pipelineVersion: 
  padding: 4
  spriteSizeExpand: 0
  spriteCategoryList:
    categories: []
  spritePackingTag: 
  resliceFromLayer: 0
  mosaicPSDLayers: []
  rigPSDLayers:
  - name: "\u56FE\u5C42 3"
    spriteName: 
    isGroup: 0
    parentIndex: -1
    spriteID: 323c5337c033040ed97c676b614b5b2a
    layerID: 5
    mosaicPosition: {x: 0, y: 0}
    flatten: 0
    isImported: 0
    isVisible: 0
  - name: "\u56FE\u5C42 3"
    spriteName: "\u56FE\u5C42 3"
    isGroup: 0
    parentIndex: -1
    spriteID: 9c9be303873114096ad6299b41a3c6cc
    layerID: 4
    mosaicPosition: {x: 4, y: 567}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: "\u56FE\u5C42 2"
    spriteName: "\u56FE\u5C42 2"
    isGroup: 0
    parentIndex: -1
    spriteID: 22aee846b5c82429fae78a3d57251df1
    layerID: 3
    mosaicPosition: {x: 4, y: 4}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: "\u56FE\u5C42 3"
    spriteName: 
    isGroup: 0
    parentIndex: -1
    spriteID: 5aa0510e3ea8947ae920df213d09a8d6
    layerID: 2
    mosaicPosition: {x: 0, y: 0}
    flatten: 0
    isImported: 0
    isVisible: 0
  - name: "\u80CC\u666F"
    spriteName: 
    isGroup: 0
    parentIndex: -1
    spriteID: f73f5dac9a4d543d8b226ac147f0e734
    layerID: 1
    mosaicPosition: {x: 0, y: 0}
    flatten: 0
    isImported: 0
    isVisible: 0
  sharedRigPSDLayers: []
  pSDLayerImportSetting: []
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  spriteSizeExpandChanged: 0
  generateGOHierarchy: 0
  textureAssetName: 
  prefabAssetName: 
  spriteLibAssetName: 
  skeletonAssetName: 
  secondarySpriteTextures: []
