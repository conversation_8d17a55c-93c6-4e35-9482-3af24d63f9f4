fileFormatVersion: 2
guid: 9344b542de24e4fddb8c07a6ed1b3e81
importerOverride:
  nativeImporterType: 2089858483
  scriptedImporterType:
    serializedVersion: 2
    Hash: b10b55a1aa6a1aa32521bc0cab59632e
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: b2a9591990af98743ba3ff7cf1000886, type: 3}
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 1
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 1
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 512
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 0
    wrapV: 0
    wrapW: 0
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  spriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
    spritePosition: {x: 0, y: 0}
  mosaicSpriteImportData:
  - name: Eyelid_4
    originalName: 
    pivot: {x: 0.5, y: 0.5}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 304
      y: 248
      width: 115
      height: 240
    spriteID: 2c6bc8214bd5849dda8551367f5eac9f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 304, y: 248}
    spritePosition: {x: 67, y: 9}
  - name: Eyelid_3
    originalName: 
    pivot: {x: 0.5, y: 0.5}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 164
      y: 248
      width: 132
      height: 238
    spriteID: a2f8d995760364defb073d304b6de05a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 164, y: 248}
    spritePosition: {x: 59, y: 11}
  - name: Eyelid_2
    originalName: 
    pivot: {x: 0.5, y: 0.5}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 158
      height: 236
    spriteID: b9ccc5b9c15d74b1cbce323dd93deb73
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
    spritePosition: {x: 46, y: 11}
  - name: Eyelid_1
    originalName: 
    pivot: {x: 0.5, y: 0.5}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 4
      y: 248
      width: 152
      height: 237
    spriteID: c3fe0929e304d4f529bbfa079c82e17c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 248}
    spritePosition: {x: 49, y: 9}
  - name: Eyeball
    originalName: 
    pivot: {x: 0.5, y: 0.5}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 427
      y: 248
      width: 75
      height: 76
    spriteID: 4a2cdd8dbcc4140669fab1384b1358d9
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 427, y: 248}
    spritePosition: {x: 87, y: 89}
  - name: White
    originalName: 
    pivot: {x: 0.5, y: 0.5}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 170
      y: 4
      width: 96
      height: 229
    spriteID: 9a7bd3f5cc50a4b9098a106d87feb319
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 170, y: 4}
    spritePosition: {x: 77, y: 15}
  rigSpriteImportData:
  - name: "\u56FE\u5C42 6"
    originalName: 
    pivot: {x: 0.5, y: 0.5}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 304
      y: 248
      width: 115
      height: 240
    spriteID: 6e993ace831224129ae2bcf0a0808e86
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 304, y: 248}
    spritePosition: {x: 67, y: 9}
  - name: "\u56FE\u5C42 8"
    originalName: 
    pivot: {x: 0.5, y: 0.5}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 164
      y: 248
      width: 132
      height: 238
    spriteID: ebde2b1e63ae44a20b7015a3724c0e1f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 164, y: 248}
    spritePosition: {x: 59, y: 11}
  - name: "\u56FE\u5C42 7"
    originalName: 
    pivot: {x: 0.5, y: 0.5}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 158
      height: 236
    spriteID: ad95592cec30e4f45a5ea9c0ccce01c8
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
    spritePosition: {x: 46, y: 11}
  - name: "\u56FE\u5C42 9"
    originalName: 
    pivot: {x: 0.5, y: 0.5}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 4
      y: 248
      width: 152
      height: 237
    spriteID: 36cf4035a92bc4ec3893003173afa79c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 248}
    spritePosition: {x: 49, y: 9}
  - name: "\u4ECE\u9009\u533A"
    originalName: 
    pivot: {x: 0.5, y: 0.5}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 427
      y: 248
      width: 75
      height: 76
    spriteID: fdf0a3d7ee2d946989ca2340750f65d7
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 427, y: 248}
    spritePosition: {x: 87, y: 89}
  - name: "\u56FE\u5C42 3"
    originalName: 
    pivot: {x: 0.5, y: 0.5}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 170
      y: 4
      width: 96
      height: 229
    spriteID: 489b714ceeb974bcd9224c82db2f96e2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 170, y: 4}
    spritePosition: {x: 77, y: 15}
  characterData:
    bones: []
    parts: []
    dimension: {x: 0, y: 0}
    characterGroups: []
    pivot: {x: 0, y: 0}
    boneReadOnly: 0
  sharedRigSpriteImportData: []
  sharedRigCharacterData:
    bones: []
    parts: []
    dimension: {x: 0, y: 0}
    characterGroups: []
    pivot: {x: 0, y: 0}
    boneReadOnly: 0
  platformSettings:
  - name: DefaultTexturePlatform
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  - name: Standalone
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  - name: WebGL
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  mosaicLayers: 1
  characterMode: 0
  documentPivot: {x: 0, y: 0}
  documentAlignment: 7
  importHiddenLayers: 0
  layerMappingOption: 2
  generatePhysicsShape: 0
  paperDollMode: 0
  keepDupilcateSpriteName: 1
  skeletonAssetReferenceID: 
  pipeline: {instanceID: 0}
  pipelineVersion: 
  padding: 4
  spriteSizeExpand: 0
  spriteCategoryList:
    categories: []
  spritePackingTag: 
  resliceFromLayer: 0
  mosaicPSDLayers:
  - name: Eyelid_4
    spriteName: Eyelid_4
    isGroup: 0
    parentIndex: -1
    spriteID: 2c6bc8214bd5849dda8551367f5eac9f
    layerID: 11
    mosaicPosition: {x: 304, y: 248}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: Eyelid_3
    spriteName: Eyelid_3
    isGroup: 0
    parentIndex: -1
    spriteID: a2f8d995760364defb073d304b6de05a
    layerID: 10
    mosaicPosition: {x: 164, y: 248}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: Eyelid_2
    spriteName: Eyelid_2
    isGroup: 0
    parentIndex: -1
    spriteID: b9ccc5b9c15d74b1cbce323dd93deb73
    layerID: 9
    mosaicPosition: {x: 4, y: 4}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: Eyelid_1
    spriteName: Eyelid_1
    isGroup: 0
    parentIndex: -1
    spriteID: c3fe0929e304d4f529bbfa079c82e17c
    layerID: 8
    mosaicPosition: {x: 4, y: 248}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: "\u65B0\u5EFA\u7EC4"
    spriteName: 
    isGroup: 1
    parentIndex: -1
    spriteID: 3526feb44018a43c0a38a65460b2445d
    layerID: 7
    mosaicPosition: {x: 0, y: 0}
    flatten: 0
    isImported: 0
    isVisible: 1
  - name: Eyeball
    spriteName: Eyeball
    isGroup: 0
    parentIndex: 4
    spriteID: 4a2cdd8dbcc4140669fab1384b1358d9
    layerID: 4
    mosaicPosition: {x: 427, y: 248}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: White
    spriteName: White
    isGroup: 0
    parentIndex: 4
    spriteID: 9a7bd3f5cc50a4b9098a106d87feb319
    layerID: 3
    mosaicPosition: {x: 170, y: 4}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: "\u80CC\u666F"
    spriteName: 
    isGroup: 0
    parentIndex: -1
    spriteID: 4b8c06a59e8fc410aaf9822292d86363
    layerID: 1
    mosaicPosition: {x: 0, y: 0}
    flatten: 0
    isImported: 0
    isVisible: 0
  rigPSDLayers:
  - name: "\u56FE\u5C42 6"
    spriteName: "\u56FE\u5C42 6"
    isGroup: 0
    parentIndex: -1
    spriteID: 6e993ace831224129ae2bcf0a0808e86
    layerID: 11
    mosaicPosition: {x: 304, y: 248}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: "\u56FE\u5C42 8"
    spriteName: "\u56FE\u5C42 8"
    isGroup: 0
    parentIndex: -1
    spriteID: ebde2b1e63ae44a20b7015a3724c0e1f
    layerID: 10
    mosaicPosition: {x: 164, y: 248}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: "\u56FE\u5C42 7"
    spriteName: "\u56FE\u5C42 7"
    isGroup: 0
    parentIndex: -1
    spriteID: ad95592cec30e4f45a5ea9c0ccce01c8
    layerID: 9
    mosaicPosition: {x: 4, y: 4}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: "\u56FE\u5C42 9"
    spriteName: "\u56FE\u5C42 9"
    isGroup: 0
    parentIndex: -1
    spriteID: 36cf4035a92bc4ec3893003173afa79c
    layerID: 8
    mosaicPosition: {x: 4, y: 248}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: "\u65B0\u5EFA\u7EC4"
    spriteName: 
    isGroup: 1
    parentIndex: -1
    spriteID: 17c347ca45fb943628b5ffc59f81c2ac
    layerID: 7
    mosaicPosition: {x: 0, y: 0}
    flatten: 0
    isImported: 0
    isVisible: 1
  - name: "\u4ECE\u9009\u533A"
    spriteName: "\u4ECE\u9009\u533A"
    isGroup: 0
    parentIndex: 4
    spriteID: fdf0a3d7ee2d946989ca2340750f65d7
    layerID: 4
    mosaicPosition: {x: 427, y: 248}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: "\u56FE\u5C42 3"
    spriteName: "\u56FE\u5C42 3"
    isGroup: 0
    parentIndex: 4
    spriteID: 489b714ceeb974bcd9224c82db2f96e2
    layerID: 3
    mosaicPosition: {x: 170, y: 4}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: "\u80CC\u666F"
    spriteName: 
    isGroup: 0
    parentIndex: -1
    spriteID: 367687af4ea4e4053823a962c5e1d499
    layerID: 1
    mosaicPosition: {x: 0, y: 0}
    flatten: 0
    isImported: 0
    isVisible: 0
  sharedRigPSDLayers: []
  pSDLayerImportSetting: []
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  spriteSizeExpandChanged: 0
  generateGOHierarchy: 0
  textureAssetName: 
  prefabAssetName: 
  spriteLibAssetName: 
  skeletonAssetName: 
  secondarySpriteTextures: []
