fileFormatVersion: 2
guid: 520c0141e8bf94ea7bf09c10d277685d
importerOverride:
  nativeImporterType: 2089858483
  scriptedImporterType:
    serializedVersion: 2
    Hash: b10b55a1aa6a1aa32521bc0cab59632e
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: b2a9591990af98743ba3ff7cf1000886, type: 3}
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 1
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 1
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 512
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  spriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
    spritePosition: {x: 0, y: 0}
  mosaicSpriteImportData: []
  rigSpriteImportData:
  - name: "\u56FE\u5C42 3"
    originalName: 
    pivot: {x: 0.5, y: 0.5}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 342
      y: 4
      width: 115
      height: 668
    spriteID: afbd669d198a9449b9ca3d9d5cfc1d16
    spriteBone:
    - name: Leg
      guid: b85e0069ca43a4b6ea752106ffcf2f20
      position: {x: 292.14743, y: 0.000033932745, z: 0}
      rotation: {x: 0, y: 0, z: 0.005602011, w: 0.9999844}
      length: 287.2782
      parentId: 1
      color:
        serializedVersion: 2
        rgba: 4294967040
    - name: Thigh
      guid: 8de8bcb02110443fe911dd128d6822bc
      position: {x: 57.810303, y: 603.7022, z: 0}
      rotation: {x: 0, y: 0, z: -0.7090687, w: 0.70513946}
      length: 292.1473
      parentId: -1
      color:
        serializedVersion: 2
        rgba: 4278255360
    spriteOutline: []
    vertices:
    - position: {x: 115, y: 0.25601196}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 115, y: 72.987}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 115, y: 137.721}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 115, y: 202.452}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 115, y: 267.185}
      boneWeight:
        weight0: 0.82298744
        weight1: 0.17701256
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 1
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 115, y: 331.918}
      boneWeight:
        weight0: 0.91356003
        weight1: 0.08643999
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 115, y: 396.648}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 115, y: 461.38}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 115, y: 526.11304}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 115, y: 590.846}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 115, y: 655.578}
      boneWeight:
        weight0: 0
        weight1: 1
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 1
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 115, y: 668}
      boneWeight:
        weight0: 0
        weight1: 1
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 1
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 50.27, y: 667.891}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 0, y: 667.53503}
      boneWeight:
        weight0: 0
        weight1: 1
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 1
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 0, y: 588.802}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 0, y: 524.07}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 0, y: 459.336}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 0, y: 394.604}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 0, y: 329.874}
      boneWeight:
        weight0: 0.5523323
        weight1: 0.44766775
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 0, y: 265.141}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 0.0009994507, y: 200.409}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 0.0019989014, y: 135.676}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 0.020999908, y: 70.94501}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 0.19599915, y: 6.217987}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    - position: {x: 0.52199936, y: 0}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 0
        boneIndex2: 0
        boneIndex3: 0
    spritePhysicsOutline: []
    indices: 000000001700000001000000090000000e0000000c000000090000000c0000000a000000080000000f0000000e000000080000000e0000000900000007000000100000000f000000070000000f000000080000000600000011000000100000000600000010000000070000000500000012000000110000000a0000000c0000000b0000000500000011000000060000000400000012000000050000000300000014000000130000000300000013000000040000000200000015000000140000000200000014000000030000000100000017000000160000000100000016000000150000000100000015000000020000000000000018000000170000000400000013000000120000000c0000000e0000000d000000
    edges:
    - {x: 0, y: 1}
    - {x: 0, y: 24}
    - {x: 1, y: 2}
    - {x: 2, y: 3}
    - {x: 3, y: 4}
    - {x: 4, y: 5}
    - {x: 5, y: 6}
    - {x: 6, y: 7}
    - {x: 7, y: 8}
    - {x: 8, y: 9}
    - {x: 9, y: 10}
    - {x: 10, y: 11}
    - {x: 11, y: 12}
    - {x: 12, y: 13}
    - {x: 13, y: 14}
    - {x: 14, y: 15}
    - {x: 15, y: 16}
    - {x: 16, y: 17}
    - {x: 17, y: 18}
    - {x: 18, y: 19}
    - {x: 19, y: 20}
    - {x: 20, y: 21}
    - {x: 21, y: 22}
    - {x: 22, y: 23}
    - {x: 23, y: 24}
    tessellationDetail: 0
    uvTransform: {x: -644, y: -268}
    spritePosition: {x: 986, y: 272}
  - name: "\u56FE\u5C42 2"
    originalName: 
    pivot: {x: 0.5, y: 0.5}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 330
      height: 555
    spriteID: 64ae209a9b3004014bddd003c452f8ed
    spriteBone:
    - name: Thigh
      guid: 8de8bcb02110443fe911dd128d6822bc
      position: {x: -30.118242, y: -5.793593, z: 0}
      rotation: {x: 0, y: 0, z: -0.9999557, w: 0.00941446}
      length: 292.1474
      parentId: 1
      color:
        serializedVersion: 2
        rgba: 4278255360
    - name: Hip
      guid: f638a5a97c83547699b413f8b2d2f588
      position: {x: 103.75281, y: 85.95276, z: 0}
      rotation: {x: 0, y: 0, z: 0.6984327, w: 0.7156758}
      length: 135.0586
      parentId: -1
      color:
        serializedVersion: 2
        rgba: 4278190335
    - name: Chest
      guid: bfc18ce45dc2548708d7c18850b5ae05
      position: {x: 135.0586, y: 0.00000049567006, z: 0}
      rotation: {x: 0, y: 0, z: 0.012192367, w: 0.9999257}
      length: 276.6233
      parentId: 1
      color:
        serializedVersion: 2
        rgba: 4278255615
    spriteOutline: []
    vertices:
    - position: {x: 111, y: 20}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 1
        boneIndex2: 1
        boneIndex3: 1
    - position: {x: 135.556, y: 29.556}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 1
        boneIndex2: 1
        boneIndex3: 1
    - position: {x: 166.613, y: 64.616}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 1
        boneIndex2: 1
        boneIndex3: 1
    - position: {x: 189.898, y: 120.896}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 1
        boneIndex2: 1
        boneIndex3: 1
    - position: {x: 202.644, y: 178.35199}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 1
        boneIndex2: 1
        boneIndex3: 1
    - position: {x: 210.736, y: 236.965}
      boneWeight:
        weight0: 0.73043746
        weight1: 0.26956254
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 1
        boneIndex2: 1
        boneIndex3: 1
    - position: {x: 212.47, y: 297.67}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 1
        boneIndex2: 1
        boneIndex3: 1
    - position: {x: 208.601, y: 357.911}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 1
        boneIndex2: 1
        boneIndex3: 1
    - position: {x: 199.528, y: 416.639}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 1
        boneIndex2: 1
        boneIndex3: 1
    - position: {x: 183.754, y: 473.223}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 1
        boneIndex2: 1
        boneIndex3: 1
    - position: {x: 156.718, y: 524.283}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 1
        boneIndex2: 1
        boneIndex3: 1
    - position: {x: 128.1, y: 551.16003}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 1
        boneIndex2: 1
        boneIndex3: 1
    - position: {x: 102.619, y: 555}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 1
        boneIndex2: 1
        boneIndex3: 1
    - position: {x: 79.516, y: 548.515}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 1
        boneIndex2: 1
        boneIndex3: 1
    - position: {x: 57.362, y: 526.033}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 1
        boneIndex2: 1
        boneIndex3: 1
    - position: {x: 34.349, y: 488.735}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 1
        boneIndex2: 1
        boneIndex3: 1
    - position: {x: 16.307999, y: 432.395}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 1
        boneIndex2: 1
        boneIndex3: 1
    - position: {x: 4.473007, y: 374.601}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 1
        boneIndex2: 1
        boneIndex3: 1
    - position: {x: 0.44599912, y: 314.595}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 1
        boneIndex2: 1
        boneIndex3: 1
    - position: {x: 0.2899933, y: 252.962}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 2
        boneIndex1: 1
        boneIndex2: 1
        boneIndex3: 1
    - position: {x: 5.4329987, y: 194.373}
      boneWeight:
        weight0: 0.9685754
        weight1: 0.031424582
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 2
        boneIndex2: 1
        boneIndex3: 1
    - position: {x: 17.186996, y: 135.629}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 1
        boneIndex2: 1
        boneIndex3: 1
    - position: {x: 38.253, y: 79.015}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 1
        boneIndex1: 1
        boneIndex2: 1
        boneIndex3: 1
    - position: {x: 60.394, y: 44.606995}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 1
        boneIndex2: 1
        boneIndex3: 1
    - position: {x: 84.373, y: 24.973007}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 1
        boneIndex2: 1
        boneIndex3: 1
    - position: {x: 107.43, y: 19.681}
      boneWeight:
        weight0: 1
        weight1: 0
        weight2: 0
        weight3: 0
        boneIndex0: 0
        boneIndex1: 1
        boneIndex2: 1
        boneIndex3: 1
    spritePhysicsOutline: []
    indices: 0000000018000000010000000a0000000e0000000c0000000a0000000c0000000b00000009000000100000000f000000090000000f0000000a0000000800000011000000100000000800000010000000090000000700000012000000110000000700000011000000080000000600000013000000120000000600000012000000070000000500000014000000130000000500000013000000060000000400000015000000140000000400000014000000050000000300000016000000150000000300000015000000040000000200000017000000160000000200000016000000030000000100000018000000170000000100000017000000020000000000000019000000180000000a0000000f0000000e0000000c0000000e0000000d000000
    edges:
    - {x: 0, y: 1}
    - {x: 0, y: 25}
    - {x: 1, y: 2}
    - {x: 2, y: 3}
    - {x: 3, y: 4}
    - {x: 4, y: 5}
    - {x: 5, y: 6}
    - {x: 6, y: 7}
    - {x: 7, y: 8}
    - {x: 8, y: 9}
    - {x: 9, y: 10}
    - {x: 10, y: 11}
    - {x: 11, y: 12}
    - {x: 12, y: 13}
    - {x: 13, y: 14}
    - {x: 14, y: 15}
    - {x: 15, y: 16}
    - {x: 16, y: 17}
    - {x: 17, y: 18}
    - {x: 18, y: 19}
    - {x: 19, y: 20}
    - {x: 20, y: 21}
    - {x: 21, y: 22}
    - {x: 22, y: 23}
    - {x: 23, y: 24}
    - {x: 24, y: 25}
    tessellationDetail: 0
    uvTransform: {x: -931, y: -816}
    spritePosition: {x: 935, y: 820}
  characterData:
    bones:
    - name: Hip
      guid: f638a5a97c83547699b413f8b2d2f588
      position: {x: 1038.7528, y: 905.95276, z: 0}
      rotation: {x: 0, y: 0, z: 0.6984327, w: 0.7156758}
      length: 135.0586
      parentId: -1
      color:
        serializedVersion: 2
        rgba: 4278190335
    - name: Chest
      guid: bfc18ce45dc2548708d7c18850b5ae05
      position: {x: 135.05858, y: -0.000007282736, z: 0}
      rotation: {x: 0, y: 0, z: 0.012192367, w: 0.9999257}
      length: 276.6233
      parentId: 0
      color:
        serializedVersion: 2
        rgba: 4278255615
    - name: Thigh
      guid: 8de8bcb02110443fe911dd128d6822bc
      position: {x: -30.118252, y: -5.793608, z: 0}
      rotation: {x: 0, y: -0, z: -0.9999557, w: 0.009414491}
      length: 292.1473
      parentId: 0
      color:
        serializedVersion: 2
        rgba: 4278255360
    - name: Leg
      guid: b85e0069ca43a4b6ea752106ffcf2f20
      position: {x: 292.14734, y: -0.000013216452, z: 0}
      rotation: {x: 0, y: 0, z: 0.0056019784, w: 0.9999844}
      length: 287.27817
      parentId: 2
      color:
        serializedVersion: 2
        rgba: 4294967040
    parts:
    - spritePosition:
        x: 986
        y: 272
        width: 115
        height: 668
      spriteId: afbd669d198a9449b9ca3d9d5cfc1d16
      bones: 0300000002000000
      parentGroup: 0
      order: 0
    - spritePosition:
        x: 935
        y: 820
        width: 330
        height: 555
      spriteId: 64ae209a9b3004014bddd003c452f8ed
      bones: 020000000000000001000000
      parentGroup: 0
      order: 0
    dimension: {x: 0, y: 0}
    characterGroups: []
    pivot: {x: 0.5038585, y: 0.118649565}
    boneReadOnly: 0
  sharedRigSpriteImportData: []
  sharedRigCharacterData:
    bones: []
    parts: []
    dimension: {x: 0, y: 0}
    characterGroups: []
    pivot: {x: 0, y: 0}
    boneReadOnly: 0
  platformSettings:
  - name: DefaultTexturePlatform
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  - name: Standalone
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  - name: WebGL
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  mosaicLayers: 1
  characterMode: 1
  documentPivot: {x: 0.5038585, y: 0.118649565}
  documentAlignment: 9
  importHiddenLayers: 0
  layerMappingOption: 2
  generatePhysicsShape: 0
  paperDollMode: 0
  keepDupilcateSpriteName: 1
  skeletonAssetReferenceID: 
  pipeline: {instanceID: 0}
  pipelineVersion: 
  padding: 4
  spriteSizeExpand: 0
  spriteCategoryList:
    categories: []
  spritePackingTag: 
  resliceFromLayer: 0
  mosaicPSDLayers: []
  rigPSDLayers:
  - name: "\u56FE\u5C42 3"
    spriteName: Foot_R
    isGroup: 0
    parentIndex: -1
    spriteID: 392ec2dc44a06423298d0f4cddd59199
    layerID: 5
    mosaicPosition: {x: 488, y: 871}
    flatten: 0
    isImported: 0
    isVisible: 0
  - name: "\u56FE\u5C42 3"
    spriteName: "\u56FE\u5C42 3"
    isGroup: 0
    parentIndex: -1
    spriteID: afbd669d198a9449b9ca3d9d5cfc1d16
    layerID: 4
    mosaicPosition: {x: 342, y: 4}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: "\u56FE\u5C42 2"
    spriteName: "\u56FE\u5C42 2"
    isGroup: 0
    parentIndex: -1
    spriteID: 64ae209a9b3004014bddd003c452f8ed
    layerID: 3
    mosaicPosition: {x: 4, y: 4}
    flatten: 0
    isImported: 1
    isVisible: 1
  - name: "\u56FE\u5C42 3"
    spriteName: 
    isGroup: 0
    parentIndex: -1
    spriteID: d018f26e8584142e897ead2085be4583
    layerID: 2
    mosaicPosition: {x: 0, y: 0}
    flatten: 0
    isImported: 0
    isVisible: 0
  - name: "\u80CC\u666F"
    spriteName: 
    isGroup: 0
    parentIndex: -1
    spriteID: 10610700c3ce948d59adbeee4dacd41b
    layerID: 1
    mosaicPosition: {x: 0, y: 0}
    flatten: 0
    isImported: 0
    isVisible: 0
  sharedRigPSDLayers: []
  pSDLayerImportSetting: []
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  spriteSizeExpandChanged: 0
  generateGOHierarchy: 0
  textureAssetName: 
  prefabAssetName: 
  spriteLibAssetName: 
  skeletonAssetName: 
  secondarySpriteTextures: []
