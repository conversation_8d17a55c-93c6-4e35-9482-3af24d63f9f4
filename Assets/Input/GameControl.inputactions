{"name": "GameControl", "maps": [{"name": "Game", "id": "41b42aef-ef08-49b4-aaad-eb0b7350d41a", "actions": [{"name": "Move", "type": "PassThrough", "id": "c4118ab5-b5aa-481c-8046-55a272cea7ed", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Jump", "type": "<PERSON><PERSON>", "id": "2655c6e0-0e03-4827-872d-0e9d8f578517", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "AttackA", "type": "<PERSON><PERSON>", "id": "91085f54-389c-48c0-9d63-e17d003aebd4", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "AttackB", "type": "<PERSON><PERSON>", "id": "69bdecb1-9936-492e-bad6-b1cfeb0e3d6e", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Dash", "type": "<PERSON><PERSON>", "id": "10afda1e-17a2-4112-be9d-8e1ccd15913b", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Block", "type": "<PERSON><PERSON>", "id": "2e1b0dc6-2af3-4994-a4f5-2da7c38041e5", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "AttackRemote", "type": "<PERSON><PERSON>", "id": "2259eba6-f4c0-49ca-b9b2-f179e322beef", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "<PERSON>rab", "type": "<PERSON><PERSON>", "id": "e6b281fd-8ca2-4da4-9716-bbb0b09f570d", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Focus", "type": "<PERSON><PERSON>", "id": "4466cb82-75ec-453c-8e21-9a01a3377c09", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Test", "type": "<PERSON><PERSON>", "id": "0655ca0e-28ad-47fe-b571-ec312b140080", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "ChangeFocus", "type": "<PERSON><PERSON>", "id": "ad68a05c-f448-45f0-8213-5b001299f064", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}], "bindings": [{"name": "AWSD", "id": "59ff0715-ea90-4b19-ba9f-49f755e14891", "path": "2DVector", "interactions": "", "processors": "", "groups": "", "action": "Move", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "c9fb9704-14c2-44d7-8963-a32ad3b07670", "path": "<Keyboard>/w", "interactions": "", "processors": "", "groups": "", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "3cdf200a-54d8-4dec-a830-a6d0abb918f6", "path": "<Keyboard>/s", "interactions": "", "processors": "", "groups": "", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "ce37d16f-67cf-46e0-9f33-8fdb6125f5ba", "path": "<Keyboard>/a", "interactions": "", "processors": "", "groups": "", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "2d3c51b9-2f7e-49ed-b1fa-1b4cc2f53070", "path": "<Keyboard>/d", "interactions": "", "processors": "", "groups": "", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "7fd2eb5a-7085-4e1f-8842-7b9e4f02cad2", "path": "<Keyboard>/k", "interactions": "", "processors": "", "groups": "", "action": "Jump", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "93837647-8055-4c54-a8ee-0144ff24e1ce", "path": "<Keyboard>/a", "interactions": "", "processors": "", "groups": "", "action": "Test", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "bc2098fd-1a96-4bf0-8195-fc599190b0df", "path": "<Keyboard>/h", "interactions": "", "processors": "", "groups": "", "action": "AttackA", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "f70eab78-18ac-4a58-812e-24e5cb6fa920", "path": "<Keyboard>/j", "interactions": "", "processors": "", "groups": "", "action": "AttackB", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "7f0cf844-cda4-447c-aab9-47a144a31cde", "path": "<Keyboard>/o", "interactions": "", "processors": "", "groups": "", "action": "Dash", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "04f35b02-faa1-40a2-b32a-b09524820d34", "path": "<Keyboard>/k", "interactions": "", "processors": "", "groups": "", "action": "AttackRemote", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "b9808fad-9bf0-47f3-834c-d1b57f4077fa", "path": "<Keyboard>/l", "interactions": "", "processors": "", "groups": "", "action": "<PERSON>rab", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "0425833b-cbfa-4f4f-b40b-18e467cef4f4", "path": "<Keyboard>/o", "interactions": "", "processors": "", "groups": "", "action": "Block", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "b8b20d49-03ef-4903-b26f-f6b5b83f13aa", "path": "<Keyboard>/space", "interactions": "", "processors": "", "groups": "", "action": "Focus", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "517058aa-3685-40d3-910d-6aeeb0813e67", "path": "<Keyboard>/tab", "interactions": "", "processors": "", "groups": "", "action": "ChangeFocus", "isComposite": false, "isPartOfComposite": false}]}], "controlSchemes": []}