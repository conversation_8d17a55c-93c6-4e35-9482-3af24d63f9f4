%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-5950276457378365545
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 030f85c3f73729f4f976f66ffb23b875, type: 3}
  m_Name: AnimationPlayableAsset
  m_EditorClassIdentifier: 
  m_Clip: {fileID: 7400000, guid: 956628adaa30c4c8c866b4623827024f, type: 2}
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_UseTrackMatchFields: 1
  m_MatchTargetFields: 63
  m_RemoveStartOffset: 1
  m_ApplyFootIK: 1
  m_Loop: 0
  m_Version: 1
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bfda56da833e2384a9677cd3c976a436, type: 3}
  m_Name: MengPoSoup
  m_EditorClassIdentifier: 
  m_Version: 0
  m_Tracks:
  - {fileID: 8470623987074094056}
  m_FixedDuration: 0
  m_EditorSettings:
    m_Framerate: 60
    m_ScenePreview: 1
  m_DurationMode: 0
  m_MarkerTrack: {fileID: 0}
--- !u!114 &2467806489358787348
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 030f85c3f73729f4f976f66ffb23b875, type: 3}
  m_Name: AnimationPlayableAsset(Clone)
  m_EditorClassIdentifier: 
  m_Clip: {fileID: 7400000, guid: 956628adaa30c4c8c866b4623827024f, type: 2}
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_UseTrackMatchFields: 1
  m_MatchTargetFields: 63
  m_RemoveStartOffset: 1
  m_ApplyFootIK: 1
  m_Loop: 0
  m_Version: 1
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
--- !u!114 &8470623987074094056
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d21dcc2386d650c4597f3633c75a1f98, type: 3}
  m_Name: Animation Track
  m_EditorClassIdentifier: 
  m_Version: 3
  m_AnimClip: {fileID: 0}
  m_Locked: 0
  m_Muted: 0
  m_CustomPlayableFullTypename: 
  m_Curves: {fileID: 0}
  m_Parent: {fileID: 11400000}
  m_Children: []
  m_Clips:
  - m_Version: 1
    m_Start: 0
    m_ClipIn: 0
    m_Asset: {fileID: -5950276457378365545}
    m_Duration: 6.35
    m_TimeScale: 1
    m_ParentTrack: {fileID: 8470623987074094056}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 1
    m_PreExtrapolationMode: 1
    m_PostExtrapolationTime: 0
    m_PreExtrapolationTime: 0
    m_DisplayName: Pour
  - m_Version: 1
    m_Start: 6.35
    m_ClipIn: 0
    m_Asset: {fileID: 2467806489358787348}
    m_Duration: 6.35
    m_TimeScale: 1
    m_ParentTrack: {fileID: 8470623987074094056}
    m_EaseInDuration: 0
    m_EaseOutDuration: 0
    m_BlendInDuration: -1
    m_BlendOutDuration: -1
    m_MixInCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_MixOutCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_BlendInCurveMode: 0
    m_BlendOutCurveMode: 0
    m_ExposedParameterNames: []
    m_AnimationCurves: {fileID: 0}
    m_Recordable: 0
    m_PostExtrapolationMode: 1
    m_PreExtrapolationMode: 1
    m_PostExtrapolationTime: Infinity
    m_PreExtrapolationTime: 0
    m_DisplayName: Pour
  m_Markers:
    m_Objects: []
  m_InfiniteClipPreExtrapolation: 0
  m_InfiniteClipPostExtrapolation: 0
  m_InfiniteClipOffsetPosition: {x: 0, y: 0, z: 0}
  m_InfiniteClipOffsetEulerAngles: {x: 0, y: 0, z: 0}
  m_InfiniteClipTimeOffset: 0
  m_InfiniteClipRemoveOffset: 0
  m_InfiniteClipApplyFootIK: 1
  mInfiniteClipLoop: 0
  m_MatchTargetFields: 63
  m_Position: {x: 0, y: 0, z: 0}
  m_EulerAngles: {x: 0, y: 0, z: 0}
  m_AvatarMask: {fileID: 0}
  m_ApplyAvatarMask: 0
  m_TrackOffset: 0
  m_InfiniteClip: {fileID: 0}
  m_OpenClipOffsetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_Rotation: {x: 0, y: 0, z: 0, w: 1}
  m_ApplyOffsets: 0
