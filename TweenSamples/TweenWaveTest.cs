using System.Linq;
using UnityEngine;

[RequireComponent(typeof(LineRenderer))]
public class TweenWaveTest : MonoBehaviour
{
  public Transform root;
  public Transform target;
  [Range(0.1f, 2f)]
  [SerializeField] float precisionMultiplier = 1f;
  [SerializeField] float amplitude = 1f;
  [SerializeField] int cycles = 1;
  [SerializeField] float segmentLength = 0.25f;
  [Head<PERSON>("Tween Settings")]
  [SerializeField] float duration = 1f;

  private bool increase = true;

  private LineRenderer lineRenderer;

  void OnDrawGizmos()
  {
    Gizmos.color = Color.green;
    Gizmos.DrawSphere(root.position, 0.2f);
    Gizmos.color = Color.yellow;
    Gizmos.DrawSphere(target.position, 0.2f);
  }

  void Start()
  {
    lineRenderer = GetComponent<LineRenderer>();
    Initialize();

    TweenCurve();
  }

  void Update()
  {
    // if (root.hasChanged || target.hasChanged)
    // {
    //   CalculateNext();
    // }
  }

  void TweenCurve()
  {
    var currPoints = new Vector3[lineRenderer.positionCount];
    lineRenderer.GetPositions(currPoints);

    var nextPoints = CalculateNext();
    MakeUpPoints(ref currPoints, ref nextPoints);

    var tweens = currPoints.Select((x, i) => {
      return new TweenSingleTime{
        duration = duration,
        values = new object[] { x, nextPoints[i] }
      };
    }).ToArray();

    gameObject.TweenCompose(
      tweens,
      values =>
      {
        lineRenderer.positionCount = currPoints.Length;
        lineRenderer.SetPositions(values.Cast<Vector3>().ToArray());
      }
    )
    .SetOnComplete(TweenCurve)
    .Start();
  }

  void Initialize()
  {
    var direction = target.position - root.position;
    var dir = direction.normalized;
    var length = direction.magnitude;
    int pointCount = GetPointCount(length);
    Vector3[] points = new Vector3[pointCount];

    for (int i = 0; i < pointCount; i++)
    {
      points[i] = root.position + dir * i * length / (pointCount - 1);
    }

    lineRenderer.positionCount = pointCount;
    lineRenderer.SetPositions(points);
  }

  void RandomSettings()
  {
    increase = cycles >= 8 ? false : cycles <= 0 ? true : increase;
    cycles += increase ? 1 : -1;
  }

  Vector3[] CalculateNext()
  {
    RandomSettings();
    var direction = target.position - root.position;
    var dir = direction.normalized;
    var length = direction.magnitude;
    int pointCount = GetPointCount(length);
    Vector3[] points = new Vector3[pointCount];

    for (int i = 0; i < pointCount; i++)
    {
      var basePoint = root.position + dir * i * length / (pointCount - 1);
      var range = Mathf.PI * cycles;
      var offset = Mathf.Sin(i * range / (pointCount - 1));
      points[i] = basePoint + (Vector3)Vector2.Perpendicular(dir) * offset * amplitude;
    }

    return points;
    // lineRenderer.positionCount = pointCount;
    // lineRenderer.SetPositions(points);
  }

  int GetPointCount(float length)
  {
    // return Mathf.CeilToInt(length * cycles * amplitude * 1.5f);
    return 20;
  }
  public static void MakeUpPoints(ref Vector3[] a, ref Vector3[] b)
  {
    // 获取两个数组的长度
    int lengthA = a.Length;
    int lengthB = b.Length;

    // 如果 a 比 b 长，填充 b 的剩余部分为 a 的对应元素
    if (lengthA > lengthB)
    {
      b = b.Concat(a.Skip(lengthB).Take(lengthA - lengthB)).ToArray();
    }
    // 如果 b 比 a 长，填充 a 的剩余部分为 b 的对应元素
    else if (lengthB > lengthA)
    {
      a = a.Concat(b.Skip(lengthA).Take(lengthB - lengthA)).ToArray();
    }
    // 如果长度相等，不做任何操作
  }
}
