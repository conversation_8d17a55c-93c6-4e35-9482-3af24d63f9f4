
using UnityEngine;
using UnityEngine.Splines;


public class TweenTestTwistMove : MonoBehaviour
{
  public enum MoveStartPoint
  {
    ToTop,
    Center,
    ToBottom,
  }

  struct PositionAndTangent
  {
    public Vector3 position;
    public Vector3 tangent;
  }

  [SerializeField] SplineContainer splineContainer;
  [SerializeField] AnimationCurve timeCurve;

  [SerializeField] int cycles = 1;
  [SerializeField] float duration = 2f;
  [SerializeField] float turnDuration = 1f;
  [SerializeField] Vector2 moveOffset = new(20f, 5f);
  [SerializeField] MoveStartPoint moveStartPoint = MoveStartPoint.ToTop;

  private ITween currentTween;

  void Update()
  {
    if (Input.GetKeyDown(KeyCode.P))
    {
      if (currentTween.IsPaused)
      {
        currentTween.Resume();
      }
      else
      {
        currentTween.Pause();
      }
    }
    if (Input.GetKeyDown(KeyCode.Space))
    {
      Run();
    }
  }

  void Run()
  {
    GetSinMoveTween(true)
      .Then(() => GetSemiCircle(true))
      .Then(() => GetSinMoveTween(false))
      .Then(() => GetSemiCircle(false).SetOnComplete(Run))
      .Start();
  }

  ITween GetSemiCircle(bool right = true)
  {
    Vector2 center = (Vector2)transform.position + (right ? Vector2.up : Vector2.down) * moveOffset.y;

    Vector2 initialPosition = transform.position;

    return currentTween = gameObject.TweenCompose(new TweenSingle[]{
      new TweenSingleTime{
        duration = turnDuration,
        values = new object[] { 0f, 180f },
        InterpolateFunc = (start, end, t, _, _) => {
          Vector2 dir = (initialPosition - center).normalized;
          var angle = Mathf.Lerp((float)start, (float)end, t);
          var rotation = Quaternion.AngleAxis(angle, Vector3.forward);
          var rotatedDir = rotation * dir;
          var tangent = Vector2.Perpendicular(rotatedDir);

          return new PositionAndTangent{
            position = (Vector3)center + rotatedDir * moveOffset.y,
            tangent = tangent
          };
        }
      }
    }, vals => {
        PositionAndTangent r = (PositionAndTangent)vals[0];
        transform.position = r.position;

        var angle = Vector2.SignedAngle(Vector2.right, r.tangent);
        transform.rotation = Quaternion.AngleAxis(angle, Vector3.forward);
    });
  }
  ITween GetSinMoveTween(bool direction = true)
  {
    return currentTween = gameObject.TweenCompose(new TweenSingle[]{
        new TweenSingleTime{
          duration = duration,
          easeFunc = t => {
            if (direction)
              return Mathf.Sin(t * 2 * cycles * Mathf.PI - Mathf.PI / 2) + 1f;
            return Mathf.Sin(t * 2 * cycles * Mathf.PI + Mathf.PI / 2) - 1f;
          },
          values = new object[] {
            transform.position.y,
            transform.position.y + moveOffset.y
          },
        },
        new TweenSingleTime{
          duration = duration,
          values = new object[] {
            transform.position.x,
            transform.position.x + (direction ? moveOffset.x : -moveOffset.x)
          }
        }
      }, vals =>
      {
        var y = (float)vals[0];
        var x = (float)vals[1];

        var oldPosition = transform.position;
        var newPosition = new Vector2(x, y);
        transform.SetPositionAndRotation(newPosition, QuaternionExt.Tangent(oldPosition, newPosition));
      });
  }
}