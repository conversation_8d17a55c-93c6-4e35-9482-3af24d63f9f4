
using UnityEngine;
using UnityEngine.Splines;

public class TweenTestCircle : MonoBehaviour
{ 
  struct PositionAndTangent
  {
    public Vector3 position;
    public Vector3 tangent;
  }

  [SerializeField] SplineContainer splineContainer;
  [SerializeField] AnimationCurve timeCurve;

  private ITween tween;

  private float opacity;

  private Vector2 centerPoint;
  private float radius;

  private Vector2 initialPosition = new(-5f, 5f);

  public float GetOpacity()
  {
    return 0f;
  }
  public void SetOpacity(float op)
  {
    opacity = op;
    Debug.Log(op);
  }

  public void RecalculateCenter()
  {
    radius = Random.Range(5f, 10f);
    var rad = Random.Range(0f, 2 * Mathf.PI);
    var centerDir = new Vector2(Mathf.Cos(rad), Mathf.Sin(rad));
    centerPoint = initialPosition + centerDir * radius;
  }

  void Update()
  {
    if (Input.GetKeyDown(KeyCode.Space))
    {
      RecalculateCenter();
      gameObject.TweenCompose(new TweenSingle[]{
        new TweenSingleTime{
          duration = 2f,
          easeType = EaseType.EaseInOutElastic,
          values = new object[] { 
            initialPosition,
          },
          InterpolateFunc = (start, end, t, reverse, _) => {
            Vector2 dir = ((Vector2)start - centerPoint).normalized;
            var angle = Mathf.Lerp(0, 360, t);
            var rotation = Quaternion.AngleAxis(angle, Vector3.forward);
            var rotatedDir = rotation * dir;
            var tangent = Vector2.Perpendicular(rotatedDir);

            return new PositionAndTangent{
              position = (Vector3)centerPoint + rotatedDir * radius,
              tangent = reverse ? -tangent : tangent
            };
          }
        }
      }, vals => {
        PositionAndTangent r = (PositionAndTangent)vals[0];
        transform.position = r.position;

        var angle = Vector2.SignedAngle(Vector2.right, r.tangent);
        transform.rotation = Quaternion.AngleAxis(angle, Vector3.forward);

      })
      .SetLoop(-1, true)
      .SetOnLoop(() => {
        RecalculateCenter();
      })
      .Start();
    }
  }

}