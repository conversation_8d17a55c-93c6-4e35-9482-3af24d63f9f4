using UnityEngine;

// 阿基米螺线 r=a+bθ
// 对数螺线 r=ae bθ

public enum SpiralType
{
  Arch,
  Log,
}
 
public class SpiralMovement : MonoBehaviour
{
    [SerializeField] Transform target;
    [SerializeField] float duration = 2;
    [SerializeField] SpiralType spiralType;
    [Header("阿基米德螺线")]
    [SerializeField] float archA = 1;
    [SerializeField] float archB = 0;
    [Header("对数螺线")]
    [SerializeField] float logA = 1;
    [SerializeField] float logB = 1;
    
    void Update()
    {
      if (Input.GetKeyDown(KeyCode.Space))
      {
        var r = Vector2.Distance(target.position, transform.position);
        var angle = GetAngle(r);
        var dir = (transform.position - target.position).normalized;

        gameObject.TweenCompose(
          new TweenSingle[]{
            new TweenSingleTime{
              duration = duration,
              easeType = EaseType.EaseInExpo,
              values = new object[]{
                angle,
                0f
              },
              InterpolateFunc = (start, end, t, _, _) => {
                var currentAngle = Mathf.Lerp((float)start, (float)end, t);
                var currentR = GetR(currentAngle);
                var angleDifference = angle - currentAngle;
                var currentDir = Quaternion.AngleAxis(angleDifference, Vector3.forward) * dir;
                var tangent = Vector2.Perpendicular(currentDir);

                return new PositionAndTangent{
                  position = target.position + currentDir * currentR,
                  tangent = tangent
                };
              }
            }
          },
          vals => {
            PositionAndTangent r = (PositionAndTangent)vals[0];
            transform.SetPositionAndTangent(r);
          }
        ).Start();


      }
    }

    float GetAngle(float r)
    {
      switch (spiralType)
      {
        case SpiralType.Arch:
          return (r - archB) / archA;
        case SpiralType.Log:
          return (1f / logB) * Mathf.Log(r / logA);
      }
      return 0;
    }

    float GetR(float angle)
    {
      switch (spiralType)
      {
        case SpiralType.Arch:
          return angle * archA + archB;
        case SpiralType.Log:
          return logA * Mathf.Exp(logB * angle);
      }
      return 0;
    }

}