
using UnityEngine;
using UnityEngine.Splines;

public class TweenTestSemiCircle : MonoBehaviour
{ 
  struct PositionAndTangent
  {
    public Vector3 position;
    public Vector3 tangent;
  }

  [SerializeField] SplineContainer splineContainer;
  [SerializeField] AnimationCurve timeCurve;

  private float opacity;

  private float radius;
  private Vector2 centerPoint;

  private Vector2 initialPosition = new(-5f, 5f);
  private int loopTimes = 0;

  public float GetOpacity()
  {
    return 0f;
  }
  public void SetOpacity(float op)
  {
    opacity = op;
    Debug.Log(op);
  }

  public void RecalculateCenter()
  {
    initialPosition = transform.position;
    radius = Random.Range(5f, 10f);
    centerPoint = initialPosition + Vector2.right * radius;
  }

  void Update()
  {
    if (Input.GetKeyDown(KeyCode.Space))
    {
      RecalculateCenter();
      var tween = gameObject.TweenCompose(new TweenSingle[]{
        new TweenSingleTime{
          duration = 2f,
          easeType = EaseType.EaseInOutBounce,
          values = new object[] { 
            0f,
            180f
          },
          InterpolateFunc = (start, end, t, reverse, _) => {
            Vector2 dir = (initialPosition - centerPoint).normalized;
            var angle = Mathf.Lerp((float)start, (float)end, t);
            angle = loopTimes % 2 == 0 ? angle : -angle;
            var rotation = Quaternion.AngleAxis(angle, Vector3.forward);
            var rotatedDir = rotation * dir;
            var tangent = Vector2.Perpendicular(rotatedDir);

            return new PositionAndTangent{
              position = (Vector3)centerPoint + rotatedDir * radius,
              tangent = loopTimes % 2 == 0 ? tangent : -tangent
            };
          }
        }
      }, vals => {
        PositionAndTangent r = (PositionAndTangent)vals[0];
        transform.position = r.position;

        var angle = Vector2.SignedAngle(Vector2.right, r.tangent);
        transform.rotation = Quaternion.AngleAxis(angle, Vector3.forward);
      });

      tween.SetLoop(-1, false)
      .SetOnLoop(() => {
        loopTimes = tween.LoopTimes;
        RecalculateCenter();
      })
      .Start();
    }
  }
}