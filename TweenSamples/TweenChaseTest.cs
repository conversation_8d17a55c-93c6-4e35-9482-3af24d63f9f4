
using System;
using UnityEngine;
using UnityEngine.Splines;

public class TweenChaseTest : MonoBehaviour
{ 
  [SerializeField] Transform chaseTarget;
  [SerializeField] float speed;
  [SerializeField] float rotationSpeed;

  private ITween tween;

  private Vector3 initialPosition;
  private Vector3 unBiasPosition;

  private Quaternion initialRotation;

  void Start()
  {
    initialPosition = transform.position;
    unBiasPosition = initialPosition;

    initialRotation = transform.rotation;
  }

  public float GetOpacity()
  {
    return 0f;
  }
  public void SetOpacity(float op)
  {
    Debug.Log(op);
  }

  void Update()
  {
    if (Input.GetKeyDown(KeyCode.Space))
    {
      transform.TweenTowardsRotation(() => {
        var dir = (chaseTarget.position - transform.position).normalized;
        var angle = Vector2.SignedAngle(Vector2.right, dir);
        return Quaternion.AngleAxis(angle, Vector3.forward);
      }, rotationSpeed).Start();

      transform.TweenTowardsPosition(() => {
        return chaseTarget.position;
      }, speed).Start();
    }
  }
}